CREATE TABLE `t_boot_dict`
(
  `id`            varchar(32) NOT NULL,
  `c_dict_name`   varchar(100) DEFAULT NULL COMMENT '字典名称',
  `c_dict_code`   varchar(100) DEFAULT NULL COMMENT '字典编码',
  `c_description` varchar(255) DEFAULT NULL COMMENT '字典描述',
  `c_type`        varchar(2)   DEFAULT NULL COMMENT '字典类型',
  `c_create_time` bigint(20) DEFAULT '0' COMMENT '创建时间',
  `c_update_time` bigint(20) DEFAULT '0' COMMENT '修改时间',
  `c_create_man`  varchar(32)  DEFAULT NULL COMMENT '创建人',
  `c_state`       varchar(1)   DEFAULT NULL COMMENT '数据状态',
  `c_update_man`  varchar(32)  DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`),
  KEY             `t_boot_dict_c_dict_code_IDX` (`c_dict_code`,`c_state`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据字典表';

CREATE TABLE `t_boot_dict_detail`
(
  `id`            varchar(32) NOT NULL,
  `dict_id`       varchar(32)  DEFAULT NULL COMMENT '字典 id',
  `c_key`         varchar(100) DEFAULT NULL COMMENT '字典详情key',
  `c_value`       varchar(100) DEFAULT NULL COMMENT '字典详情值',
  `c_description` varchar(255) DEFAULT NULL COMMENT '描述',
  `c_sort`        int(11) DEFAULT '0' COMMENT '排序值',
  `c_is_enable`   varchar(2)   DEFAULT NULL COMMENT '是否启用：1 启用、0 禁用',
  `c_create_time` bigint(20) DEFAULT '0' COMMENT '创建时间',
  `c_update_time` bigint(20) DEFAULT '0' COMMENT '修改时间',
  `c_create_man`  varchar(32)  DEFAULT NULL COMMENT '创建人',
  `c_state`       varchar(1)   DEFAULT NULL COMMENT '数据状态',
  `c_update_man`  varchar(32)  DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`),
  KEY             `t_boot_dict_detail_dict_id_IDX` (`dict_id`,`c_is_enable`,`c_state`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='字典详情表';