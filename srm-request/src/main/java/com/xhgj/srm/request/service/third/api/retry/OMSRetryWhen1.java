package com.xhgj.srm.request.service.third.api.retry;/**
 * @since 2025/6/13 13:29
 */

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.dtflys.forest.callback.RetryWhen;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.xhiot.boot.mvc.base.ResultBean;

/**
 *<AUTHOR>
 *@date 2025/6/13 13:29:52
 *@description
 */
public class OMSRetryWhen1 implements RetryWhen {

  @Override
  public boolean retryWhen(ForestRequest req, ForestResponse res) {
    // 判断条件1
    boolean flag1 = res.getStatusCode() < 200 || res.getStatusCode() > 302;
    if (flag1) {
      return true;
    }
    // 判断条件2，转换为ResultBean code为1
    Object result = res.getResult();
    // 转换为ResultBean
    try {
      ResultBean<?> resultBean = JSON.parseObject(Convert.toStr(result), ResultBean.class);
      if (resultBean != null && resultBean.getCode() == 1) {
        return true;
      }
      return false;
    } catch (Exception e) {
      return false;
    }
  }
}
