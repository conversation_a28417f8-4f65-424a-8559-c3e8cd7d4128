package com.xhgj.srm.request.service.third.erp.sap.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.validation.constraints.NotBlank;

/**
 * OmsAdjustOrderStatusParam
 */
@NoArgsConstructor
@Data
public class OmsAdjustOrderStatusParam {

  @ApiModelProperty("客户订单号")
  @NotBlank(message = "客户订单号不能为空")
  private String orderNo;

  @ApiModelProperty("供应商订单id（履约对应派单id）")
  @NotBlank(message = "供应商订单id（履约对应派单id）不能为空")
  private String supplierOrderId;

  @ApiModelProperty("申请人")
  private String applyMan;

  @ApiModelProperty("申请时间")
  private String applyTime;

}
