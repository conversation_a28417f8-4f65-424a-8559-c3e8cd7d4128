package com.xhgj.srm.request.service.third.api.retry;/**
 * @since 2025/6/13 14:01
 */

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.retryer.BackOffRetryer;

/**
 * <AUTHOR>
 */
public class OMSRetryer1 extends BackOffRetryer {

  public OMSRetryer1(ForestRequest request) {
    super(request);
  }

  /**
   * 重写 nextInterval 方法
   * 该方法用于指定每次重试的时间间隔
   * @param currentCount 当前重试次数
   * @return 时间间隔 (时间单位为毫秒)
   */
  @Override
  protected long nextInterval(int currentCount) {
    // 第一次1s
    if (currentCount == 0) {
      return 1000;
    }
    // 第二次2s
    if (currentCount == 1) {
      return 2000;
    }
    return 5000;
  }
}

