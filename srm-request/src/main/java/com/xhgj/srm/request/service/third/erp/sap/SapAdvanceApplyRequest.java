package com.xhgj.srm.request.service.third.erp.sap;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.Constants_Sap;
import com.xhgj.srm.common.enums.AccountingPeriodEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.enums.order.OrderPaymentSource;
import com.xhgj.srm.jpa.dao.FinancialDao;
import com.xhgj.srm.jpa.dao.LandingMerchantContractDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhgj.srm.jpa.entity.OrderRefundCollection;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.request.RequestConstants;
import com.xhgj.srm.request.enums.SAPMethod;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam.DATADTO.HEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult;
import com.xhgj.srm.request.service.third.sap.impl.SAPServiceImpl;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
  *@ClassName SapAdvanceApplyRequest
  *<AUTHOR>
  *@Date 2024/1/8 19:36
*/
@Component
@Slf4j
public class SapAdvanceApplyRequest  extends BaseSapRequest {
  @Resource
  private SupplierPerformanceDao supplierPerformanceDao;
  @Resource
  private LandingMerchantContractDao landingMerchantContractDao;
  @Resource
  private FinancialDao financialDao;
  @Resource
  private SupplierRepository supplierRepository;
  @Autowired
  private UserRepository userRepository;
  @Autowired private UserToGroupDao userToGroupDao;
  @Autowired private GroupRepository groupRepository;

  /**
   * 预付款申请 / 提款申请
   * @param param
   * @return
   * @throws RuntimeException
   *
   * @deprecated 请使用 {@link SAPServiceImpl#sapAdvanceApplyWithLockGroup(AdvanceApplyParam)}
   */
  @Deprecated
  public boolean sapAdvanceApply(AdvanceApplyParam param)
      throws RuntimeException {
    Assert.notNull(param);
    AdvanceApplyResult result;
    try {
      String responseBody = postSap(SAPMethod.ZFM_MM_034,param);
      result =
          JSON.parseObject(responseBody, new TypeReference<AdvanceApplyResult>() {});
      if(StrUtil.equals(result.getReturnX().getType(),Constants_Sap.SUCCESS_TYPE)){
        return true;
      }else {
        throw new CheckException("调用sap接口失败:" + result.getReturnX().getMsg());
      }
    }catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error("调用sap接口失败", e);
      throw new CheckException("SAP网络异常请求失败，请联系管理员处理。请求时间"+ DateUtil.format(new Date(),
          DatePattern.NORM_DATETIME_PATTERN));
    }
  }

  public static AdvanceApplyParam buildAdvanceApplyParamByAdvanceReversal(
      PaymentApplyRecord applyRecord
      , SupplierOrder supplierOrder, Supplier supplier, FinancialVoucher financialVoucher,
      PaymentApplyDetail paymentApplyDetail, User user) {
    Assert.notNull(supplier);
    Assert.notBlank(supplier.getMdmCode());
    //调用MDM的SAP接口 预付款申请
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    AdvanceApplyParam.DATADTO.HEADDTO headdto = new AdvanceApplyParam.DATADTO.HEADDTO();
    headdto.setSrmid(applyRecord.getPaymentApplyNo().substring(4,14));
    headdto.setBukrs(supplierOrder.getGroupCode());
    //付款用途，默认4
    headdto.setZzmfklx("4");
    headdto.setLifnr(supplier.getMdmCode());
    //当前时间 格式 20240101
    String nowDate =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    headdto.setZzmsqrq(nowDate);
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(user.getRealName());
    List<ITEMDTO> itemdtoList = new ArrayList<>();
    AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO itemdto = new AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO();
    itemdto.setZzmfksqhxmh("1");
    itemdto.setEbeln(supplierOrder.getCode());
    BigDecimal applyAdvancePrice = paymentApplyDetail.getApplyAdvancePrice();
    itemdto.setZzmsfje(applyAdvancePrice.toPlainString());
    itemdto.setBrtwr(supplierOrder.getPrice().toPlainString());
    itemdto.setZfbdt(DateUtils.formatTimeStampToStr(paymentApplyDetail.getAdvanceDate(), DatePattern.PURE_DATE_PATTERN));
    itemdto.setZbd1t("0");
    PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(financialVoucher.getPaymentType());
    if (payTypeSAPEnums == null) {
      payTypeSAPEnums = PayTypeSAPEnums.fromKey(financialVoucher.getPaymentType());
    }
    itemdto.setZlsch(payTypeSAPEnums != null ? payTypeSAPEnums.getCode() : "");
    itemdto.setText2(payTypeSAPEnums != null ? payTypeSAPEnums.getName() : "");
    //付款状态 默认传4
    itemdto.setZzmfkzt("");
    itemdto.setBednr(StrUtil.sub(supplierOrder.getPurchaseMan(), 4,
        supplierOrder.getPurchaseMan().length()));
    itemdto.setEknam(supplierOrder.getPurchaseDept());
    itemdto.setZzmmxbz(paymentApplyDetail.getRemark());
    itemdto.setBankl(paymentApplyDetail.getBankCode());
    itemdto.setBankn(paymentApplyDetail.getBankAccount());
    itemdto.setKoinh(paymentApplyDetail.getAccountName());
    itemdto.setBelnr(financialVoucher.getFinancialVoucherNo());
    itemdto.setGjahr(financialVoucher.getAccountingYear());
    itemdtoList.add(itemdto);
    headdto.setZzmfksqzje(paymentApplyDetail.getApplyAdvancePrice().toPlainString());
    headdto.setItem(itemdtoList);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }

  public AdvanceApplyParam buildOrderPaymentSAPParams(OrderPayment orderPayment,
      List<OrderPaymentToOrder> orderPaymentToOrder, List<Order> orderList, Supplier supplier) {
    Assert.notNull(orderPayment);
    Assert.notBlank(supplier.getMdmCode());
    //调用MDM的SAP接口 预付款申请
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    AdvanceApplyParam.DATADTO.HEADDTO headdto = new AdvanceApplyParam.DATADTO.HEADDTO();
    headdto.setSrmid(StrUtil.subSufByLength(orderPayment.getPaymentNo(), 10));
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.throwExceptionIfNotFind(orderList.get(0).getTitleOfTheContract());
    headdto.setBukrs(titleOfTheContractEnum.getCode());
    //付款用途，默认4
    headdto.setZzmfklx(RequestConstants.PRE_PAY_STATE);
    headdto.setLifnr(supplier.getMdmCode());
    //当前时间 格式 20240101
    String nowDate =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    headdto.setZzmsqrq(nowDate);
    User user;
    if (ObjectUtil.equals(orderPayment.getSource(), OrderPaymentSource.FRONTEND.getCode())
        || ObjectUtil.equals(orderPayment.getSource(), OrderPaymentSource.MOBILE.getCode())
    ) {
      user = userRepository.findFirstByRealNameAndState(Constants.USER_NAME_GUO_JIA_LEI,
          Constants.STATE_OK);
      if (user == null) {
        throw new CheckException("未找到用户" + Constants.USER_NAME_GUO_JIA_LEI + "，请联系管理员");
      }
    } else {
      user = userRepository.findById(orderPayment.getSubmitId())
          .orElseThrow(() -> CheckException.noFindException(User.class, orderPayment.getSubmitId()));
    }
    // 取第一个部门
    AtomicReference<String> dept = new AtomicReference<>(StrUtil.EMPTY);
    userToGroupDao.getUserToGroupList(user.getId()).stream().map(UserToGroup::getDeptId)
        .filter(StrUtil::isNotBlank).findFirst().ifPresent(deptId->{
          dept.set(groupRepository.findById(deptId).map(Group::getName).orElse(null));
        });

    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(user.getRealName());

    List<ITEMDTO> itemdtoList = new ArrayList<>();
    BigDecimal sumItemPrice = BigDecimal.ZERO;
    Map<String, Order> orderMap =
        orderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));
    int i = 0;
    for (OrderPaymentToOrder paymentToOrder : orderPaymentToOrder) {
      String relationId = paymentToOrder.getRelationId();
      Order order = orderMap.get(relationId);
      if (order == null) {
        throw new CheckException("订单不存在，订单id：" + relationId);
      }
      ITEMDTO itemdto = new ITEMDTO();
      itemdto.setZzmfksqhxmh(String.valueOf((i+1)));
      itemdto.setEbeln(order.getErpOrderNo());
      BigDecimal price = paymentToOrder.getApplyPrice();
      sumItemPrice = NumberUtil.add(sumItemPrice,price);
      itemdto.setZzmsfje(price.setScale(2, RoundingMode.HALF_UP).toPlainString());
      itemdto.setBrtwr(price.setScale(2, RoundingMode.HALF_UP).toPlainString());
      itemdto.setZfbdt(nowDate);
      itemdto.setZzmqwfkrq(nowDate);
      itemdto.setZbd1t("1");
      PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(orderPayment.getPayType());
      if (payTypeSAPEnums == null) {
        payTypeSAPEnums = PayTypeSAPEnums.fromKey(orderPayment.getPayType());
      }
      if (payTypeSAPEnums == PayTypeSAPEnums.OHTER) {
        itemdto.setText2(
            StrUtil.isNotBlank(orderPayment.getPayOtherDesc()) ? orderPayment.getPayOtherDesc() : PayTypeSAPEnums.OHTER.getName());
      } else {
        itemdto.setText2(payTypeSAPEnums != null ? payTypeSAPEnums.getName() : "");
      }
      itemdto.setZlsch(payTypeSAPEnums != null ? payTypeSAPEnums.getCode() : "");
      itemdto.setZzmfkzt("");
      itemdto.setBednr(user.getRealName());
      itemdto.setEknam(dept.get());

      StringBuilder mark = getMark(supplier, order);
      itemdto.setZzmmxbz(mark.toString());
      String brankCode = null;
      String bankAccount = null;
      if (OrderPaymentSource.BACKEND.getCode().equals(orderPayment.getSource()) || orderPayment.getSource() == null) {
        brankCode = orderPayment.getBankCode();
        bankAccount = orderPayment.getBankAccount();
      } else {
        // 这里已经是按时间倒序了，所以应该取最后一条
        List<Financial> financialList =
            financialDao.getBySupplierIdAndGroupCode(supplier.getId(), titleOfTheContractEnum.getCode());
        if (CollUtil.isNotEmpty(financialList)) {
          // 取最后一条
          brankCode = financialList.get(financialList.size() - 1).getBankCode();
          bankAccount = financialList.get(financialList.size() - 1).getBankNum();
        }
      }
      itemdto.setBankl(brankCode);
      itemdto.setBankn(bankAccount);
      itemdto.setKoinh(supplier.getEnterpriseName());
      itemdtoList.add(itemdto);
      i++;
    }

    headdto.setItem(itemdtoList);
    headdto.setZzmfksqzje(sumItemPrice.toPlainString());
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }

  public StringBuilder getMark(Supplier supplier, Order order) {
    StringBuilder mark = new StringBuilder(order.getTypeName()).append("，");
    Optional.ofNullable(supplierPerformanceDao.getBySupplierIdAndPlatformCode(supplier.getId(),
        order.getType())).ifPresent(
            supplierPerformance -> {
              String landingContractId = supplierPerformance.getLandingContractId();
              if (StrUtil.isNotBlank(landingContractId)) {
                Optional.ofNullable(landingMerchantContractDao.get(landingContractId))
                    .ifPresent(landingMerchantContract -> {
                      String period ;
                      if (BooleanUtil.isTrue(landingMerchantContract.getBackToBack())) {
                        period = AccountingPeriodEnum.BACK_TO_BACK.getDesc();
                      }else{
                        Integer accountingPeriod = landingMerchantContract.getAccountingPeriod();
                        if (accountingPeriod!=null) {
                          period = accountingPeriod +"天";
                        }else{
                          period = StrUtil.EMPTY;
                        }
                      }
                      if (StrUtil.isNotBlank(period)) {
                        mark.append(period).append("，");
                      }
                    });
              }
            }
    );
    mark.append(order.getOrderNo());
    Long confirmAccountOpenInvoiceTime = order.getConfirmAccountOpenInvoiceTime();
    mark.append("-进项票确认时间：").append(
        confirmAccountOpenInvoiceTime != null ? DateUtil.format(
            DateTime.of(confirmAccountOpenInvoiceTime), DatePattern.NORM_DATETIME_PATTERN)
            : StrUtil.EMPTY).append("、");
    Long confirmVoucherTime = order.getConfirmVoucherTime();
    mark.append("验收单确认时间：").append(
        confirmAccountOpenInvoiceTime != null ? DateUtil.format(
            DateTime.of(confirmVoucherTime), DatePattern.NORM_DATETIME_PATTERN)
            : StrUtil.EMPTY).append("、");
    Long customerReturnSignTime = order.getCustomerReturnSignTime();
    mark.append("客户回款确认时间：").append(
        customerReturnSignTime != null ? DateUtil.format(
            DateTime.of(customerReturnSignTime), DatePattern.NORM_DATETIME_PATTERN)
            : StrUtil.EMPTY).append("、");
    Long arrivalTime = order.getArrivalTime();
    mark.append("客户回款最晚到账时间：").append(
        arrivalTime != null ? DateUtil.format(
            DateTime.of(arrivalTime), DatePattern.NORM_DATETIME_PATTERN)
            : StrUtil.EMPTY).append("、");
    Long writeOffTime = order.getWriteOffTime();
    mark.append("客户回款最晚核销时间：").append(
        writeOffTime != null ? DateUtil.format(
            DateTime.of(writeOffTime), DatePattern.NORM_DATETIME_PATTERN)
            : StrUtil.EMPTY).append("、");
    mark.append("客户回款方式：").append(StrUtil.emptyIfNull(order.getPaymentType()));
    return mark;
  }

  public static AdvanceApplyParam buildAdvanceApplyParamByAdvanceReversal(
      PaymentApplyRecord record,
      List<PaymentApplyDetail> details,
      User user,
      Supplier supplier,
      List<FinancialVoucher> financialVouchers,
      List<SupplierOrder> supplierOrders) {
    Assert.notNull(supplier);
    Assert.notBlank(supplier.getMdmCode());
    Map<String, SupplierOrder> code2SupplierOrder = supplierOrders.stream()
        .collect(Collectors.toMap(SupplierOrder::getCode, Function.identity()));
    Map<String,FinancialVoucher> id2FinancialVoucher = financialVouchers.stream()
        .collect(Collectors.toMap(FinancialVoucher::getId, Function.identity()));

    //调用MDM的SAP接口 预付款申请
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    AdvanceApplyParam.DATADTO.HEADDTO headdto = new AdvanceApplyParam.DATADTO.HEADDTO();
    headdto.setSrmid(record.getPaymentApplyNo().substring(4, 14));
    // groupCode取supplierOrder中首条数据的groupCode
    String groupCode =
        financialVouchers.stream().map(FinancialVoucher::getGroupCode).findFirst().orElse(null);
    headdto.setBukrs(groupCode);
    //付款用途，默认4
    headdto.setZzmfklx("4");
    headdto.setLifnr(supplier.getMdmCode());
    //当前时间 格式 20240101
    String nowDate =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    headdto.setZzmsqrq(nowDate);
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(user.getRealName());
    List<ITEMDTO> itemdtoList = new ArrayList<>();
    for (PaymentApplyDetail detail : details) {
      AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO itemdto = new AdvanceApplyParam.DATADTO.HEADDTO.ITEMDTO();
      FinancialVoucher financialVoucher = id2FinancialVoucher.get(detail.getFinancialVouchersId());
      if (Boolean.TRUE.equals(financialVoucher.getInitialOrder())) {
        itemdto.setBrtwr(financialVoucher.getOrderAmount().toPlainString());
      } else {
        SupplierOrder supplierOrder = code2SupplierOrder.get(financialVoucher.getPurchaseOrderNo());
        itemdto.setBrtwr(supplierOrder.getPrice().toPlainString());
        itemdto.setBednr(
            StrUtil.sub(supplierOrder.getPurchaseMan(), 4, supplierOrder.getPurchaseMan().length()));
        itemdto.setEknam(supplierOrder.getPurchaseDept());
      }
      itemdto.setZzmfksqhxmh("1");
      itemdto.setEbeln(financialVoucher.getPurchaseOrderNo());
      BigDecimal applyAdvancePrice = detail.getApplyAdvancePrice();
      if (applyAdvancePrice == null) {
        applyAdvancePrice = BigDecimal.ZERO;
      }
      itemdto.setZzmsfje(applyAdvancePrice.toPlainString());
      Long advanceDate = detail.getAdvanceDate();
      if (advanceDate == null) {
        advanceDate = System.currentTimeMillis();
      }
      itemdto.setZfbdt(DateUtils.formatTimeStampToStr(advanceDate, DatePattern.PURE_DATE_PATTERN));
      itemdto.setZbd1t("0");
      PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(financialVoucher.getPaymentType());
      if (payTypeSAPEnums == null) {
        payTypeSAPEnums = PayTypeSAPEnums.fromKey(financialVoucher.getPaymentType());
      }
      itemdto.setZlsch(payTypeSAPEnums != null ? payTypeSAPEnums.getCode() : "");
      itemdto.setText2(payTypeSAPEnums != null ? payTypeSAPEnums.getName() : "");
      //付款状态 默认传4
      itemdto.setZzmfkzt("");
      itemdto.setZzmmxbz(detail.getRemark());
      itemdto.setBankl(detail.getBankCode());
      itemdto.setBankn(detail.getBankAccount());
      itemdto.setKoinh(detail.getAccountName());
      itemdto.setBelnr(financialVoucher.getFinancialVoucherNo());
      itemdto.setGjahr(financialVoucher.getAccountingYear());
      itemdtoList.add(itemdto);
    }
    BigDecimal price = details.stream().map(PaymentApplyDetail::getApplyAdvancePrice)
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    headdto.setZzmfksqzje(price.toPlainString());
    headdto.setItem(itemdtoList);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }

  /**
   * 构建落地商订单付款退款SAP参数
   */
  public AdvanceApplyParam buildOrderPaymentRefundSAPParams(OrderRefundCollection refundCollection,
      OrderPayment orderPayment, String refundNo, List<Order> orders) {
    AdvanceApplyParam param = new AdvanceApplyParam();
    AdvanceApplyParam.DATADTO datadto = new AdvanceApplyParam.DATADTO();
    HEADDTO headdto = new HEADDTO();
    headdto.setSrmid(refundNo);
    String titleOfTheContract = orders.stream().findFirst()
        .map(Order::getTitleOfTheContract)
        .orElse(StrUtil.EMPTY);
    TitleOfTheContractEnum titleOfTheContractEnum =
        TitleOfTheContractEnum.throwExceptionIfNotFind(titleOfTheContract);
    headdto.setBukrs(titleOfTheContractEnum.getCode());
    //3付款退款
    headdto.setZzmfklx("3");
    String mdmCode = supplierRepository.findById(orderPayment.getSupplierId()).map(Supplier::getMdmCode)
        .orElseThrow(() -> new CheckException("付款申请单对应的供应商主数据编码为空"));
    headdto.setLifnr(mdmCode);
    String nowDate =
        DateUtils.formatTimeStampToStr(System.currentTimeMillis(), DatePattern.PURE_DATE_PATTERN);
    headdto.setZzmsqrq(nowDate);
    headdto.setZzmfksqzje(refundCollection.getRefundAmount().toPlainString());
    User user = userRepository.findById(refundCollection.getUpdateMan())
        .orElseThrow(() -> CheckException.noFindException(User.class, refundCollection.getId()));
    if (StrUtil.isBlank(user.getCode()) || StrUtil.isBlank(user.getRealName())) {
      throw new CheckException("申请人工号或姓名不能为空");
    }
    headdto.setZzmsqrgh(user.getCode());
    headdto.setZzmsqr(user.getRealName());
    PayTypeSAPEnums payTypeSAPEnums = PayTypeSAPEnums.fromName(refundCollection.getPaymentType());
    if (payTypeSAPEnums == null) {
      payTypeSAPEnums = PayTypeSAPEnums.fromKey(refundCollection.getPaymentType());
    }
    List<HEADDTO.ITEMDTO> itemdtoList = new ArrayList<>();
    for (int i = 0; i < orders.size(); i++) {
      Order order = orders.get(i);
      HEADDTO.ITEMDTO itemdto = new HEADDTO.ITEMDTO();
      itemdto.setZzmfksqhxmh(String.valueOf((i+1)));
      if (StrUtil.isBlank(order.getErpOrderNo())) {
        throw new CheckException("该客户订单号："+order.getOrderNo()+" 对应的采购订单号为空");
      }
      itemdto.setEbeln(order.getErpOrderNo());
      BigDecimal price = NumberUtil.sub(order.getPrice(),order.getRefundPrice());
      itemdto.setZzmsfje(price.toPlainString());
      itemdto.setBrtwr(price.toPlainString());
      itemdto.setZfbdt(nowDate);
      itemdto.setZzmqwfkrq(nowDate);
      itemdto.setZbd1t("1");
      if (payTypeSAPEnums != null) {
        itemdto.setZlsch(payTypeSAPEnums.getCode());
        itemdto.setText2(payTypeSAPEnums.getName());
      }
      itemdto.setZzmfkzt(StrUtil.EMPTY);
      itemdto.setZzmmxbz(refundCollection.getRemark());
      itemdto.setBankl(refundCollection.getBankCode());
      itemdto.setBankn(refundCollection.getBankAccount());
      itemdto.setKoinh(refundCollection.getAccountName());
      itemdtoList.add(itemdto);
    }
    headdto.setItem(itemdtoList);
    datadto.setHead(headdto);
    param.setData(datadto);
    return param;
  }
}
