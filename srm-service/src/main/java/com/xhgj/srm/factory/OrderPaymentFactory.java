package com.xhgj.srm.factory;/**
 * @since 2025/3/18 13:13
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.component.LockUtils;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.enums.OrderErpTypeEnum;
import com.xhgj.srm.common.enums.OrderPaymentTypeEnum;
import com.xhgj.srm.common.utils.runner.JedisUtil;
import com.xhgj.srm.dto.order.SubmitPaymentOrderParams;
import com.xhgj.srm.dto.order.SubmitPaymentOrderParams.SubmitOrderItem;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrderLink;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.repository.OrderNeedPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPartialPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderLinkRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.service.OrderPaymentService;
import com.xhgj.srm.service.OrderReceiptRecordService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import static java.util.stream.Collectors.toList;

/**
 *<AUTHOR>
 *@date 2025/3/18 13:13:35
 *@description
 */
@Component
@Slf4j
public class OrderPaymentFactory {

  private static final String PAYMENT_NO_PREFIX = "FKSH";
  private static final String FKD_DATE_SUM_COUNTER = "fkd_date_sum_counter";
  public static final String ORDER_PAYMENT_CREATE_NO_LOCK = "srm:lock_for_order_payment_create_no:{}";
  @Resource
  OrderPartialPaymentRepository orderPartialPaymentRepository;
  @Resource
  OrderPaymentToOrderLinkRepository orderPaymentToOrderLinkRepository;
  @Resource
  OrderNeedPaymentRepository orderNeedPaymentRepository;
  @Resource
  OrderPaymentRepository orderPaymentRepository;
  @Resource
  OrderPaymentToOrderRepository orderPaymentToOrderRepository;
  @Resource
  OrderRepository orderRepository;
  @Resource
  JedisUtil jedisUtil;
  @Resource
  private OrderReceiptRecordService orderReceiptRecordService;
  @Resource
  private OrderPaymentService orderPaymentService;
  @Resource
  private SharePlatformService platformService;
  @Resource
  private SrmConfig srmConfig;
  @Resource
  private LockUtils lockUtils;

  /**
   * #check 某时段订单无法提交
   */
  public void checkOrderSpecialTime(List<Order> orders) {
    AtomicBoolean existSAPOnlineBeforeOrder = new AtomicBoolean(false);
    AtomicBoolean existSAPOnlineAfterOrder = new AtomicBoolean(false);
    for (Order order : orders) {
      if (BooleanUtil.isFalse(existSAPOnlineBeforeOrder.get())&&order.getCreateTime() < Constants.SAP_ONLINE_TIME) {
        existSAPOnlineBeforeOrder.set(Boolean.TRUE);
      }
      if (BooleanUtil.isFalse(existSAPOnlineAfterOrder.get())&&order.getCreateTime() >= Constants.SAP_ONLINE_TIME) {
        existSAPOnlineAfterOrder.set(Boolean.TRUE);
      }
      if (existSAPOnlineBeforeOrder.get()&&existSAPOnlineAfterOrder.get()) {
        throw new CheckException("由于咸亨ERP系统更换，三月前的订单和三月后的订单不能同时提起付款单，给您带来的不便敬请谅解");
      }
    }
  }

  /**
   * #check 存在部分付款无法付款
   */
  public void checkOrderPartPayment(List<Order> orders) {
    List<String> orderIds = orders.stream().map(Order::getId).collect(toList());
    orderIds.add("-1");
    boolean hasPartial = CollUtil.emptyIfNull(
        orderPartialPaymentRepository.findAllByOrderIdInAndStateOrderByCreateTimeDesc(orderIds,
            Constants.STATE_OK)).stream().anyMatch(
        op -> StrUtil.equals(OrderPaymentTypeEnum.PARTIAL_PAYMENT.getKey(),
            op.getOrderPaymentType()));
    if (hasPartial) {
      throw new CheckException("存在部分付款的订单，无法提交");
    }
  }

  /**
   * #check 不同签约抬头无法付款
   */
  public void checkOrderDifferentContract(List<Order> orders) {
    Set<String> titleOfTheContract =
        orders.stream().map(Order::getTitleOfTheContract).collect(Collectors.toSet());
    if (titleOfTheContract.size() > 1) {
      throw new CheckException("不同签约抬头的订单不能一起提交");
    }
  }

  /**
   * #check 不同平台无法付款
   */
  public void checkOrderDifferentPlatform(List<Order> orders) {
    Set<String> platform =
        orders.stream().map(Order::getType).collect(Collectors.toSet());
    if (platform.size() > 1) {
      throw new CheckException("不同平台的订单不能一起提交");
    }
  }

  /**
   * #check 不同供应商无法付款
   */
  public void checkOrderDifferentSupplier(List<Order> orders) {
    Set<String> supplierId =
        orders.stream().map(Order::getSupplierId).collect(Collectors.toSet());
    if (supplierId.size() > 1) {
      throw new CheckException("您同时只能对同一个供应商进行付款申请");
    }
  }

  /**
   * #check 付款禁止标签禁止付款
   * @param orders
   */
  public void checkOrderPaymentForbidden(List<Order> orders) {
    List<Order> orderList =
        orders.stream().filter(order -> Boolean.TRUE.equals(order.getProhibitionPaymentState()))
            .collect(toList());
    if (CollUtil.isNotEmpty(orderList)) {
      String orderNos = orderList.stream().map(Order::getOrderNo).collect(Collectors.joining(","));
      throw new CheckException("存在禁止付款的订单：" + orderNos);
    }
  }

  /**
   * #check 付款方式不一致
   * @param orders
   */
  public void checkOrderDifferentPayment1(List<Order> orders) {
    Set<String> allPaymentMethod = new HashSet<>();
    for (Order order : orders) {
      List<String> paymentMethods =
          orderReceiptRecordService.findPaymentMethodsByOrderId(order.getId());
      if (paymentMethods.size() > 1) {
        throw new CheckException(
            StrUtil.format("{}的客户回款存在1种以上的客户回款方式，请联系{}后台处理付款",
                order.getOrderNo(),
                StrUtil.blankToDefault(platformService.findDefaultPurchaseByCode(order.getType()),
                    srmConfig.getDefaultPurchase())));
      }
      allPaymentMethod.addAll(paymentMethods);
      if (allPaymentMethod.size() > 1) {
        throw new CheckException("不同回款方式的订单不能一起提交，请重新申请！");
      }
    }
  }

  /**
   * #check 付款方式不一致
   * @param orderNeedPayments
   */
  public void checkOrderDifferentPayment2(List<OrderNeedPayment> orderNeedPayments) {
    List<String> orderReceiptIds = orderNeedPayments.stream().map(OrderNeedPayment::getOrderReceiptId)
        .filter(StrUtil::isNotBlank).collect(toList());
    if (CollUtil.isEmpty(orderReceiptIds)) {
      return;
    }
    List<OrderReceiptRecord> orderReceiptRecords =
        orderReceiptRecordService.findByIds(orderReceiptIds);
    List<String> paymentMethods =
        orderReceiptRecords.stream().map(OrderReceiptRecord::getPaymentMethod).distinct().collect(toList());
    if (paymentMethods.size() > 1) {
      throw new CheckException("您同时只能对同一种回款方式进行付款申请");
    }
  }


  public void checkOrderPaymentAmount1(List<Order> orders) {
    for (Order order : orders) {
      BigDecimal orderActualAmount =
          NumberUtil.sub(order.getPrice(), order.getRefundPrice(), order.getCancelPrice());
      List<OrderNeedPayment> needPayments =
          orderNeedPaymentRepository.findAllByOrderIdInAndState(
              Collections.singletonList(order.getId()), Constants.STATE_OK);
      BigDecimal totalPaidAmount = needPayments.stream().map(OrderNeedPayment::getRealPaidAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      if (NumberUtil.isGreaterOrEqual(totalPaidAmount, orderActualAmount)) {
        throw new CheckException(
            StrUtil.format("{}客户订单历史付款金额已超出订单实际订货金额，不可继续付款！",
                order.getOrderNo()));
      }
    }
  }

  /**
   * 释放原有申请金额
   * @param paymentId
   */
  public List<OrderNeedPayment> releaseOrderPayment(String paymentId) {
    if (StrUtil.isBlank(paymentId)) {
      return new ArrayList<>();
    }
    List<OrderPaymentToOrderLink> paymentToOrderLinks =
        orderPaymentToOrderLinkRepository.findByPaymentId(paymentId);
    if (CollUtil.isEmpty(paymentToOrderLinks)) {
      return new ArrayList<>();
    }
    List<String> needPaymentIds =
        paymentToOrderLinks.stream().map(OrderPaymentToOrderLink::getOrderNeedPaymentId)
            .collect(toList());
    List<OrderNeedPayment> orderNeedPayments = orderNeedPaymentRepository.findAllById(needPaymentIds);
    Map<String, OrderNeedPayment> orderNeedPaymentMap =
        orderNeedPayments.stream().collect(Collectors.toMap(OrderNeedPayment::getId, orderNeedPayment -> orderNeedPayment));
    for (OrderPaymentToOrderLink paymentToOrderLink : paymentToOrderLinks) {
      OrderNeedPayment orderNeedPayment = orderNeedPaymentMap.get(paymentToOrderLink.getOrderNeedPaymentId());
      if (orderNeedPayment != null) {
        orderNeedPayment.setPendingAmount(orderNeedPayment.getPendingAmount().subtract(paymentToOrderLink.getAmount()));
      }
    }
    orderNeedPaymentRepository.saveAll(orderNeedPayments);
    orderNeedPaymentRepository.flush();
    return orderNeedPayments;
  }

  /**
   * 释放原有申请金额，并完成付款单
   * @param paymentId
   */
  public void releaseAndDoneOrderPayment(String paymentId) {
    if (StrUtil.isBlank(paymentId)) {
      return;
    }
    List<OrderPaymentToOrderLink> paymentToOrderLinks =
        orderPaymentToOrderLinkRepository.findByPaymentId(paymentId);
    if (CollUtil.isEmpty(paymentToOrderLinks)) {
      return;
    }
    List<String> needPaymentIds =
        paymentToOrderLinks.stream().map(OrderPaymentToOrderLink::getOrderNeedPaymentId)
            .collect(toList());
    List<OrderNeedPayment> orderNeedPayments = orderNeedPaymentRepository.findAllById(needPaymentIds);
    Map<String, OrderNeedPayment> orderNeedPaymentMap =
        orderNeedPayments.stream().collect(Collectors.toMap(OrderNeedPayment::getId, orderNeedPayment -> orderNeedPayment));
    for (OrderPaymentToOrderLink paymentToOrderLink : paymentToOrderLinks) {
      OrderNeedPayment orderNeedPayment = orderNeedPaymentMap.get(paymentToOrderLink.getOrderNeedPaymentId());
      if (orderNeedPayment != null) {
        // pendingAmount 可能为负数或者0就不处理
        if (orderNeedPayment.getPendingAmount().compareTo(BigDecimal.ZERO) <= 0) {
          continue;
        }
        orderNeedPayment.setPendingAmount(orderNeedPayment.getPendingAmount().subtract(paymentToOrderLink.getAmount()));
        orderNeedPayment.setPaidAmount(orderNeedPayment.getPaidAmount().add(paymentToOrderLink.getAmount()));
      }
    }
    orderNeedPaymentRepository.saveAll(orderNeedPayments);
    orderNeedPaymentRepository.flush();
  }

  /**
   * 重新锁定申请金额
   * @param paymentId
   */
  public void reLockOrderPayment(String paymentId) {
    if (StrUtil.isBlank(paymentId)) {
      return;
    }
    List<OrderPaymentToOrderLink> paymentToOrderLinks =
        orderPaymentToOrderLinkRepository.findByPaymentId(paymentId);
    if (CollUtil.isEmpty(paymentToOrderLinks)) {
      return;
    }
    List<String> needPaymentIds =
        paymentToOrderLinks.stream().map(OrderPaymentToOrderLink::getOrderNeedPaymentId)
            .collect(toList());
    List<OrderNeedPayment> orderNeedPayments = orderNeedPaymentRepository.findAllById(needPaymentIds);
    Map<String, OrderNeedPayment> orderNeedPaymentMap =
        orderNeedPayments.stream().collect(Collectors.toMap(OrderNeedPayment::getId, orderNeedPayment -> orderNeedPayment));
    for (OrderPaymentToOrderLink paymentToOrderLink : paymentToOrderLinks) {
      OrderNeedPayment orderNeedPayment = orderNeedPaymentMap.get(paymentToOrderLink.getOrderNeedPaymentId());
      if (orderNeedPayment != null) {
        orderNeedPayment.setPendingAmount(orderNeedPayment.getPendingAmount().add(paymentToOrderLink.getAmount()));
        orderNeedPayment.setPaidAmount(orderNeedPayment.getPaidAmount().subtract(paymentToOrderLink.getAmount()));
      }
    }
    orderNeedPaymentRepository.saveAll(orderNeedPayments);
    orderNeedPaymentRepository.flush();
  }

  /**
   * 锁定申请金额
   */
  public void lockOrderPayment(List<OrderPaymentToOrderLink> paymentToOrderLinks) {
    List<String> needPaymentIds = paymentToOrderLinks.stream().map(OrderPaymentToOrderLink::getOrderNeedPaymentId)
        .collect(toList());
    List<OrderNeedPayment> orderNeedPayments = orderNeedPaymentRepository.findAllById(needPaymentIds);
    Map<String, OrderNeedPayment> orderNeedPaymentMap =
        orderNeedPayments.stream().collect(Collectors.toMap(OrderNeedPayment::getId, orderNeedPayment -> orderNeedPayment));
    for (OrderPaymentToOrderLink paymentToOrderLink : paymentToOrderLinks) {
      OrderNeedPayment orderNeedPayment = orderNeedPaymentMap.get(paymentToOrderLink.getOrderNeedPaymentId());
      if (orderNeedPayment != null) {
        orderNeedPayment.setPendingAmount(orderNeedPayment.getPendingAmount().add(paymentToOrderLink.getAmount()));
      }
    }
    orderNeedPaymentRepository.saveAll(orderNeedPayments);
    orderNeedPaymentRepository.flush();
  }

  /**
   * 生成退款金额
   */
  public void returnOrderPayment(String paymentId) {
    if (StrUtil.isBlank(paymentId)) {
      return;
    }
    List<OrderPaymentToOrderLink> paymentToOrderLinks =
        orderPaymentToOrderLinkRepository.findByPaymentId(paymentId);
    if (CollUtil.isEmpty(paymentToOrderLinks)) {
      return;
    }
    List<String> needPaymentIds =
        paymentToOrderLinks.stream().map(OrderPaymentToOrderLink::getOrderNeedPaymentId)
            .collect(toList());
    List<OrderNeedPayment> orderNeedPayments = orderNeedPaymentRepository.findAllById(needPaymentIds);
    Map<String, OrderNeedPayment> orderNeedPaymentMap =
        orderNeedPayments.stream().collect(Collectors.toMap(OrderNeedPayment::getId, orderNeedPayment -> orderNeedPayment));
    for (OrderPaymentToOrderLink paymentToOrderLink : paymentToOrderLinks) {
      OrderNeedPayment orderNeedPayment = orderNeedPaymentMap.get(paymentToOrderLink.getOrderNeedPaymentId());
      if (orderNeedPayment != null) {
        orderNeedPayment.setReturnAmount(orderNeedPayment.getReturnAmount().add(paymentToOrderLink.getAmount()));
      }
    }
    orderNeedPaymentRepository.saveAll(orderNeedPayments);
    orderNeedPaymentRepository.flush();
  }


  /**
   * 原有生成逻辑
   * @param date
   * @param num
   * @return
   */
  private String getOrderPaymentCountByDate(String date,String num){
    List<RLock> rLocks = lockUtils.lockAll("id", ORDER_PAYMENT_CREATE_NO_LOCK);
    try {
    if (jedisUtil.exists(FKD_DATE_SUM_COUNTER)) {
      String redisDateSum = jedisUtil.get(FKD_DATE_SUM_COUNTER);
      String dateStr = !StringUtils.isNullOrEmpty(redisDateSum)&&redisDateSum.length()>=10?
          redisDateSum.substring(0, 10):"";
      if (!StringUtils.isNullOrEmpty(dateStr)&& Objects.equals(dateStr,date)) {
        num = redisDateSum.substring(10);
      }else{
        num = num;
      }
    }else{
      num = num;
    }
    // 转成数字型
    int  intNum = Integer.parseInt(num)+1;
    String  strNum = String.valueOf(intNum);
    jedisUtil.set(FKD_DATE_SUM_COUNTER,date+strNum);
    return num;
    } catch (Exception e) {
      log.error("付款单号生成逻辑错误", e);
      throw e;
    }finally {
      lockUtils.unlockAllLocks(rLocks);
    }
  }

  /**
   * 创建付款单
   */
  public OrderPayment createOrderPayment(SubmitPaymentOrderParams form, List<Order> orders, Supplier supplier) {
    Boolean autoDraw = true;
    List<SubmitOrderItem> items = form.getItems();
    List<String> ids = items.stream().map(SubmitOrderItem::getNeedPaymentId).collect(toList());
    List<OrderNeedPayment> orderNeedPayments = orderNeedPaymentRepository.findAllById(ids);
    List<String> orderIds =
        orderNeedPayments.stream().map(OrderNeedPayment::getOrderId).distinct().collect(toList());
    List<Order> findOrders = orderRepository.findAllById(orderIds);
    for (Order order : findOrders) {
      if (OrderErpTypeEnum.KINGDEE_ERP.getDescription().equals(order.getErpType())) {
        autoDraw = false;
      }
    }
    orders.addAll(findOrders);
    OrderPayment orderPayment = null;
    if (StrUtil.isBlank(form.getPaymentId())) {
      long time = System.currentTimeMillis();
      DateTime dateTime = DateTime.of(System.currentTimeMillis());
      long count = orderPaymentRepository.countByCreateTimeAfter(DateUtil.beginOfDay(dateTime).getTime());
      String finalCount = getOrderPaymentCountByDate(DateUtils.formatTimeStampToNormalDate(time), String.valueOf(count));
      orderPayment = new OrderPayment();
      orderPayment.setPaymentNo(PAYMENT_NO_PREFIX + DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN) + StrUtil.padPre(finalCount, 4, "0"));
//      orderPayment.setPaymentStatus(Constants_order.WAIT_APPLY_PAYMENT_TYPE);
      //7.1.0，付款单状态采用新的
      //金蝶订单提交直接变为付款中
      orderPayment.setSubmitMan(supplier.getEnterpriseName());
      orderPayment.setSubmitId(form.getUserId());
      orderPayment.setCreateTime(System.currentTimeMillis());
      orderPayment.setPaymentPrice(BigDecimal.ZERO);
      orderPayment.setState(Constants.STATE_OK);
      orderPayment.setAutoDraw(autoDraw);
      orderPayment.setSource(form.getSource());
    } else {
      orderPayment = orderPaymentRepository.findById(form.getPaymentId()).orElse(null);
      if (orderPayment == null) {
        throw new CheckException("付款单不存在");
      }
    }
    orderPayment.setApplyPrice(form.getTotalAmount());
    orderPayment.setPaymentStatus(
        StrUtil.equals(OrderErpTypeEnum.KINGDEE_ERP.getDescription(), orders.get(0).getErpType())
            ? Constants_order.DURING_PAYMENT_TYPE
            : Constants_order.UNDER_REVIEW_PAYMENT_TYPE);
    orderPayment.setRemark(form.getRemark());
    orderPayment.setSupplierId(form.getSupplierId());
    orderPayment.setOrderCount(String.valueOf(form.getItems().size()));
    orderPayment.setPayType(form.getPayType());
    orderPayment.setPayOtherDesc(form.getPayOtherDesc());
    orderPayment.setBankAccount(form.getBankAccount());
    orderPayment.setBank(form.getBank());
    orderPayment.setBankCode(form.getBankCode());
    orderPayment.setAccountName(form.getAccountName());
    return orderPayment;
  }

  public List<OrderPaymentToOrder> createOrderPaymentToOrder(SubmitPaymentOrderParams params, OrderPayment orderPayment) {
    List<OrderPaymentToOrder> res = new ArrayList<>();
    // 查询出原有的
    List<OrderPaymentToOrder> origins =
        orderPaymentToOrderRepository.getAllByOrderPaymentIdAndStateAndType(orderPayment.getId(),
            Constants.STATE_OK, Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID);
    // 原有的进行删除
    if (CollUtil.isNotEmpty(origins)) {
      for (OrderPaymentToOrder orderPaymentToOrder : origins) {
        orderPaymentToOrder.setState(Constants.STATE_DELETE);
      }
      orderPaymentToOrderRepository.saveAll(origins);
      orderPaymentToOrderRepository.flush();
    }

    List<SubmitOrderItem> items = params.getItems();
    Map<String, OrderPaymentToOrder> orderId2OrderPaymentToOrder = new HashMap<>();
    for (SubmitOrderItem item : items) {
      OrderNeedPayment orderNeedPayment =
          orderNeedPaymentRepository.findById(item.getNeedPaymentId())
              .orElseThrow(() -> new CheckException("需付款单不存在"));
      OrderPaymentToOrder orderPaymentToOrder =
          orderId2OrderPaymentToOrder.computeIfAbsent(orderNeedPayment.getOrderId(), k -> {
            OrderPaymentToOrder newOne = new OrderPaymentToOrder();
            newOne.setOrderPaymentId(orderPayment.getId());
            newOne.setRelationId(orderNeedPayment.getOrderId());
            newOne.setType(Constants_order.ORDER_TO_PAYMENT_TYPE_ORDER_ID);
            newOne.setCreateTime(System.currentTimeMillis());
            newOne.setState(Constants.STATE_OK);
            newOne.setContent(null);
            newOne.setPaymentPrice(BigDecimal.ZERO);
            newOne.setApplyPrice(BigDecimal.ZERO);
            return newOne;
          });
      orderPaymentToOrder.setApplyPrice(item.getAmount().add(orderPaymentToOrder.getApplyPrice()));
    }
    return new ArrayList<>(orderId2OrderPaymentToOrder.values());
  }

  public List<OrderPaymentToOrderLink> createOrderPaymentToOrderLink(
      SubmitPaymentOrderParams params, OrderPayment orderPayment,
      List<OrderPaymentToOrder> orderPaymentToOrders) {
    List<OrderPaymentToOrderLink> res = new ArrayList<>();
    List<OrderPaymentToOrderLink> origins = orderPaymentToOrderLinkRepository.findByPaymentId(orderPayment.getId());
    // 删除所有的
    orderPaymentToOrderLinkRepository.deleteAll(origins);
    orderPaymentToOrderLinkRepository.flush();
    Map<String, OrderPaymentToOrder> orderId2OrderPaymentToOrder =
        orderPaymentToOrders.stream().collect(Collectors.toMap(OrderPaymentToOrder::getRelationId, orderPaymentToOrder -> orderPaymentToOrder));
    for (SubmitOrderItem item : params.getItems()) {
      OrderNeedPayment orderNeedPayment =
          orderNeedPaymentRepository.findById(item.getNeedPaymentId())
              .orElseThrow(() -> new CheckException("需付款单不存在"));
      OrderPaymentToOrder orderPaymentToOrder = orderId2OrderPaymentToOrder.get(orderNeedPayment.getOrderId());
      OrderPaymentToOrderLink orderPaymentToOrderLink = new OrderPaymentToOrderLink();
      orderPaymentToOrderLink.setOrderPaymentToOrderId(orderPaymentToOrder.getId());
      orderPaymentToOrderLink.setOrderNeedPaymentId(item.getNeedPaymentId());
      orderPaymentToOrderLink.setAmount(item.getAmount());
      orderPaymentToOrderLink.setPaymentId(orderPayment.getId());
      orderPaymentToOrderLink.setPaymentNo(orderPayment.getPaymentNo());
      orderPaymentToOrderLink.setCreateTime(System.currentTimeMillis());
      orderPaymentToOrderLink.setOrderId(orderNeedPayment.getOrderId());
      orderPaymentToOrderLink.setReceiptRecordId(orderNeedPayment.getOrderReceiptId());
      res.add(orderPaymentToOrderLink);
    }
    return res;
  }

  /**
   * #check 校验预付款时间
   */
  public void checkOrderPrePaymentTime(List<Order> orders) {
    for (Order order : orders) {
      // 后台背靠背的需付款数据只需要满足签收凭证已确认、供应商已开票即可提款，无需校验是否到达预计付款日期
      if (Boolean.TRUE.equals(order.getBackToBack())) {
        boolean timeBool = order.getConfirmVoucherTime() != null;
        boolean invoiceBool = Constants.ORDER_INVOICE_STATE_PASS.equals(order.getSupplierOpenInvoiceStatus());
        if (timeBool && invoiceBool) {
          continue;
        } else {
          throw new CheckException("您勾选的订单签收凭证未确认或供应商未开票，不可提款");
        }
      } else {
        boolean notReachTime = order.getPredictPaymentTime() == null || order.getPredictPaymentTime() > System.currentTimeMillis();
        if (notReachTime) {
          throw new CheckException("您勾选的订单未到达预计付款时间，请检查");
        }
      }
    }
  }

  /**
   * #check 存在关联审核中、驳回的数据不可提交
   * @param orders
   */
  public void checkOrderPaymentStatus(List<Order> orders, String excludePaymentId) {
    for (Order order : orders) {
      List<OrderPayment> findOnes = orderPaymentService.findByOrderId(order.getId());
      // 过滤掉excludePaymentId
      if (StrUtil.isNotBlank(excludePaymentId)) {
        findOnes = findOnes.stream()
            .filter(orderPayment -> !StrUtil.equals(orderPayment.getId(), excludePaymentId))
            .collect(Collectors.toList());
      }
      boolean hasError = CollUtil.emptyIfNull(findOnes).stream()
          .anyMatch(op -> Constants_order.CHECK_PAYMENT_STATUS.contains(op.getPaymentStatus()));
      if (hasError) {
        throw new CheckException(
            StrUtil.format("{}有驳回或审核中的付款单，无法提交！", order.getOrderNo()));
      }
    }
  }

  public void checkOrderPaymentAmount2(List<OrderNeedPayment> needPayments,
      Map<String, Order> id2Order, Map<String, BigDecimal> nid2Amount) {
    for (OrderNeedPayment needPayment : needPayments) {
      Order order = id2Order.get(needPayment.getOrderId());
      BigDecimal orderActualAmount =
          NumberUtil.sub(order.getPrice(), order.getRefundPrice(), order.getCancelPrice());
      BigDecimal availableAmount = NumberUtil.mul(NumberUtil.null2Zero(needPayment.getRate()),
          NumberUtil.null2Zero(orderActualAmount)).setScale(2, RoundingMode.HALF_UP);
      BigDecimal remain = availableAmount.subtract(NumberUtil.null2Zero(needPayment.getPaidAmount()))
          .add(NumberUtil.null2Zero(needPayment.getReturnAmount()));
      if (NumberUtil.isLessOrEqual(remain, BigDecimal.ZERO)) {
        throw new CheckException(StrUtil.format("不能选择剩余可提款金额是小于等于0的数据"));
      }
      BigDecimal amount = nid2Amount.get(needPayment.getId());
      if (NumberUtil.isLess(remain, amount)) {
        throw new CheckException(StrUtil.format("{}本次提款金额超出剩余可提款金额",
            order.getOrderNo()));
      }
    }
  }

  public void checkOrderPaymentAmount1(List<Order> orders, Map<String, BigDecimal> orderId2Amount) {
    for (Order order : orders) {
      BigDecimal orderActualAmount =
          NumberUtil.sub(order.getPrice(), order.getRefundPrice(), order.getCancelPrice());
      List<OrderNeedPayment> needPayments =
          orderNeedPaymentRepository.findAllByOrderIdInAndState(
              Collections.singletonList(order.getId()), Constants.STATE_OK);
      // 此前
      BigDecimal totalPaidAmount = needPayments.stream().map(OrderNeedPayment::getRealPaidAmount)
          .reduce(BigDecimal.ZERO, BigDecimal::add);
      // 本次
      BigDecimal curTotalDraw =
          orderId2Amount.getOrDefault(order.getId(),BigDecimal.ZERO).add(totalPaidAmount);

      if (NumberUtil.isGreater(curTotalDraw, orderActualAmount)) {
        throw new CheckException(
            StrUtil.format("{}客户订单历史付款金额已超出订单实际订货金额，不可继续付款！",
                order.getOrderNo()));
      }
    }
  }
}
