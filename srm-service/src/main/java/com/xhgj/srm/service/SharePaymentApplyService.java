package com.xhgj.srm.service;

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.dto.ApprovalResultsParam;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.dto.hZero.process.PaymentApplyProcessParam;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/22 14:46
 * @Description: 共享付款申请服务
 */
public interface SharePaymentApplyService {


  /**
   * 失败重推sap
   * @param id
   */
  void syncSAP(String id);

  /**
   * 审核回调
   * @param approvalResult
   * @param status
   */
  void auditCallBack(ApprovalResult approvalResult, PaymentAuditStateEnum status);

  void auditMethod(ApprovalResultsParam params, PaymentApplyRecord paymentApplyRecord,
      String approvalResults);

  /**
   * 构建付款申请单流程参数
   * @param record
   * @param details
   * @return
   */
  PaymentApplyProcessParam buildPaymentApplyProcessParam(PaymentApplyRecord record,
      List<PaymentApplyDetail> details, User user);
}
