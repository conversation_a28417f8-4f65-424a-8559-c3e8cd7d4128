package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.ttl.TtlRunnable;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.enums.OrderErpTypeEnum;
import com.xhgj.srm.common.enums.order.OrderCancelStatus;
import com.xhgj.srm.common.execption.ThreadInterruptException;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.dto.order.DeliveryDetailDTO;
import com.xhgj.srm.dto.order.OrderDeliveryDetailDTO;
import com.xhgj.srm.dto.order.OrderReturnOMSDTO;
import com.xhgj.srm.dto.returned.OrderReturnAddParam;
import com.xhgj.srm.dto.returned.OrderReturnDetailAddParam;
import com.xhgj.srm.factory.MapStructFactory;
import com.xhgj.srm.factory.OrderCancelFactory;
import com.xhgj.srm.factory.OrderReturnFactory;
import com.xhgj.srm.factory.OrderReturnOMSDTOFactory;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.dao.OrderReturnDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderCancel;
import com.xhgj.srm.jpa.entity.OrderCancelDetail;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhgj.srm.jpa.repository.OrderCancelDetailRepository;
import com.xhgj.srm.jpa.repository.OrderCancelRepository;
import com.xhgj.srm.jpa.repository.OrderDeliveryDetailRepository;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.OrderReturnDetailRepository;
import com.xhgj.srm.jpa.repository.OrderReturnRepository;
import com.xhgj.srm.open.provider.OpenMessageCreateProvider;
import com.xhgj.srm.request.dto.OrderReturnDetailInfo;
import com.xhgj.srm.request.dto.OrderReturnInfo;
import com.xhgj.srm.request.dto.erp.ReturnResult.OrderReturnData;
import com.xhgj.srm.request.dto.erp.ReturnResult.OrderReturnData.EntryInfoDTO;
import com.xhgj.srm.request.dto.erp.RowAndNum;
import com.xhgj.srm.factory.SapFactory;
import com.xhgj.srm.request.service.third.erp.ERPRequest;
import com.xhgj.srm.request.service.third.erp.sap.MM_075Request;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Param;
import com.xhgj.srm.request.service.third.erp.sap.dto.MM_075Result;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.vo.sap.SyncOrderReturnRes;
import com.xhgj.srm.service.OrderDeliveryService;
import com.xhgj.srm.service.OrderReturnDetailTempService;
import com.xhgj.srm.service.OrderReturnTempService;
import com.xhgj.srm.service.ShareOrderReturnService;
import com.xhgj.srm.util.PreviewContextUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.AsyncUtil;
import com.xhiot.boot.core.config.BootConfig;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShareOrderReturnServiceImpl implements ShareOrderReturnService {

  @Resource
  RedissonClient redissonClient;
  @Resource
  OrderDeliveryService orderDeliveryService;
  @Resource
  private BootConfig bootConfig;
  @Resource
  private PlatformTransactionManager transactionManager;
  @Resource
  OrderDetailDao orderDetailDao;
  @Resource
  OrderReturnDao orderReturnDao;
  @Resource
  OrderReturnRepository orderReturnRepository;
  @Resource
  OrderDetailRepository orderDetailRepository;
  @Resource
  OrderRepository orderRepository;
  @Resource
  OrderReturnDetailRepository orderReturnDetailRepository;
  @Resource
  private AsyncUtil asyncUtil;
  @Resource
  private OrderReturnTempService orderReturnTempService;
  @Resource
  private OrderReturnDetailTempService orderReturnDetailTempService;
  @Resource
  private ERPRequest erpRequest;
  @Resource
  private ApplicationContext applicationContext;
  @Resource
  OrderReturnFactory orderReturnFactory;
  @Resource
  OrderDeliveryDetailRepository orderDeliveryDetailRepository;
  @Resource
  SapFactory sapFactory;
  @Resource
  SAPService sapService;
  @Resource
  OpenMessageCreateProvider openMessageCreateProvider;
  @Resource
  private OrderCancelFactory orderCancelFactory;
  @Resource
  private OrderCancelRepository orderCancelRepository;
  @Resource
  private OrderCancelDetailRepository orderCancelDetailRepository;
  private static final String ORDER_CANCEL_DETAILS = "orderCancelDetails";
  private static final String ORDER_RETURN_DETAILS = "orderReturnDetails";
  @Resource
  private OrderReturnOMSDTOFactory orderReturnOMSDTOFactory;

  @Override
  public List<OrderReturnOMSDTO> addCancelAndReturnOrder(List<OrderReturnAddParam> orderReturnAddParams) {
    // 先创建取消单、后创建退货的
    if (CollUtil.isEmpty(orderReturnAddParams)) {
      return new ArrayList<>();
    }
    RLock lock = redissonClient.getLock(Constants_LockName.ORDER_RETURN_LOCK);
    try {
      boolean preview = orderReturnAddParams.stream().anyMatch(item -> Constants.STATE_OK.equals(item.getReadOnly()));
      PreviewContextUtil.setPreviewMode(preview);
      lock.lock();
      ShareOrderReturnServiceImpl proxy = applicationContext.getBean(ShareOrderReturnServiceImpl.class);
      OrderCancel orderCancel = proxy.addOrderCancelTransactional(orderReturnAddParams);
      OrderReturnInfo orderReturnInfo = proxy.addOrderRefundTransactional(orderReturnAddParams, true, true);
      // 发送消息
      if (PreviewContextUtil.isNotPreviewMode()) {
        this.sendMsg(orderCancel, orderReturnInfo);
      }
      List<OrderCancelDetail> orderCancelDetailList =
          PreviewContextUtil.getTemporaryListData(ORDER_CANCEL_DETAILS, OrderCancelDetail.class);
      List<OrderReturnDetail> orderReturnDetails =
          PreviewContextUtil.getTemporaryListData(ORDER_RETURN_DETAILS, OrderReturnDetail.class);
      return orderReturnOMSDTOFactory.buildOrderReturnOMSDTO(orderCancelDetailList, orderReturnDetails);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员");
    } finally {
      PreviewContextUtil.clear();
      if (lock != null) {
        lock.unlock();
      }
    }
  }

  /**
   *  openMessage发送消息
   * @param orderCancel
   * @param orderReturnInfo
   */
  private void sendMsg(OrderCancel orderCancel, OrderReturnInfo orderReturnInfo) {
    // 发送消息
    if (orderCancel != null) {
      Order order = orderRepository.findById(orderCancel.getOrderId()).orElseThrow(() -> new CheckException("未找到履约单"));
      openMessageCreateProvider.cancelOrderMessage(orderCancel.getOrderId(), orderCancel.getId(), order.getSupplierId());
    }
    if (orderReturnInfo != null) {
      OrderReturn orderReturn = orderReturnInfo.getOrderReturn();
      Order order = orderReturnInfo.getOrderReturn().getOrder();
      //     创建流转消息 退货
      openMessageCreateProvider.returnOrderMessage(order.getId(), orderReturn.getId(), order.getSupplierId());
    }
  }


  @Override
  public OrderReturnInfo addOrderRefund(List<OrderReturnAddParam> orderReturnAddParams,
      boolean isSyncErpReturn, boolean sync2Sap) {
    if (CollUtil.isEmpty(orderReturnAddParams)) {
      return null;
    }
    RLock lock = redissonClient.getLock(Constants_LockName.ORDER_RETURN_LOCK);
    try {
      PreviewContextUtil.setPreviewMode(false);
      lock.lock();
      ShareOrderReturnServiceImpl proxy =
          applicationContext.getBean(ShareOrderReturnServiceImpl.class);
      return proxy.addOrderRefundTransactional(orderReturnAddParams, isSyncErpReturn,
          sync2Sap);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员");
    } finally {
      PreviewContextUtil.clear();
      if (lock != null) {
        lock.unlock();
      }
    }
  }

  public OrderReturnInfo addOrderRefundTransactional(List<OrderReturnAddParam> orderReturnAddParams,
      boolean isSyncErpReturn, boolean sync2Sap) {
    final OrderReturnInfo[] orderReturn = {null};
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        for (OrderReturnAddParam orderReturnAddParam : orderReturnAddParams) {
          // 数据判断
          if (!Objects.equals("platform", orderReturnAddParam.getSource())) {
            DingUtils.sendMsgByWarningRobot(
                "【" + bootConfig.getEnv() + "环境 " + bootConfig.getAppName() + "】 单号：【"
                    + orderReturnAddParam.getOrderNo() + "】非法调用退货接口，请及时处理！",
                bootConfig.getEnv());
            throw new CheckException("接口非法调用！");
          }
          // 校验orderNo
          if (StrUtil.isBlank(orderReturnAddParam.getOrderNo())) {
            throw new CheckException("接口请求有误");
          }
          // 校验退货详情
          log.info("订单退货入参:" + orderReturnAddParam);
          if (CollUtil.isEmpty(orderReturnAddParam.getOrderReturnDetails())) {
            throw new CheckException("参数不合法");
          }
          // 生成退货单
          orderReturn[0] = addOrderRefundRef(orderReturnAddParam, isSyncErpReturn, sync2Sap);
          if (PreviewContextUtil.isPreviewMode()) {
            status.setRollbackOnly();
          }
        }
        return null;
      });
    } catch (Exception e) {
      log.error("订单退货单创建失败，异常信息：{}", e.getMessage(), e);
      PreviewContextUtil.putTemporaryData(ORDER_RETURN_DETAILS, new ArrayList<>());
      if (e instanceof ThreadInterruptException) {
        // 如果是线程中断异常，直接抛出
        throw new CheckException(e.getMessage());
      }
    }
    return orderReturn[0];
  }

  public OrderCancel addOrderCancelTransactional(List<OrderReturnAddParam> orderReturnAddParams) {
    final OrderCancel[] orderCancel = {null};
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        for (OrderReturnAddParam orderReturnAddParam : orderReturnAddParams) {
          // 数据判断
          if (!Objects.equals("platform", orderReturnAddParam.getSource())) {
            DingUtils.sendMsgByWarningRobot(
                "【" + bootConfig.getEnv() + "环境 " + bootConfig.getAppName() + "】 单号：【"
                    + orderReturnAddParam.getOrderNo() + "】非法调用退货接口，请及时处理！",
                bootConfig.getEnv());
            throw new CheckException("接口非法调用！");
          }
          // 校验orderNo
          if (StrUtil.isBlank(orderReturnAddParam.getOrderNo())) {
            throw new CheckException("接口请求有误");
          }
          // 校验退货详情
          log.info("订单取消入参:" + orderReturnAddParam);
          if (CollUtil.isEmpty(orderReturnAddParam.getOrderReturnDetails())) {
            throw new CheckException("参数不合法");
          }
          // 生成取消单
          orderCancel[0] = this.addOrderCancel(orderReturnAddParam);
        }
        if (PreviewContextUtil.isPreviewMode()) {
          status.setRollbackOnly();
        }
        return null;
      });
    } catch (Exception e) {
      log.error("订单取消单创建失败，异常信息：{}", e.getMessage(), e);
      PreviewContextUtil.putTemporaryData(ORDER_CANCEL_DETAILS, new ArrayList<>());
      if (e instanceof ThreadInterruptException) {
        // 如果是线程中断异常，直接抛出
        throw new CheckException(e.getMessage());
      }
    }
    return orderCancel[0];
  }

  /**
   * 添加取消单
   * @param form
   */
  private OrderCancel addOrderCancel(OrderReturnAddParam form) {
    Order order = orderRepository.findFirstBySupplierOrderIdAndState(form.getSupplierOrderId(),
        Constants.STATE_OK);
    if (order == null) {
      throw new CheckException("未找到履约单");
    }
    // 1 查询订单的明细
    List<OrderDetail> orderDetailList = orderDetailRepository.findAllByOrderIdAndState(order.getId(),
        Constants.STATE_OK);
    // 生成取消单
    OrderCancel orderCancel = orderCancelFactory.createOrderCancel(order);
    orderCancelRepository.saveAndFlush(orderCancel);
    BigDecimal totalCancelNum = BigDecimal.ZERO;
    BigDecimal totalCancelPrice = BigDecimal.ZERO;
    List<OrderReturnDetailAddParam> orderReturnDetails = form.getOrderReturnDetails();
    for (OrderReturnDetailAddParam cancelOne : orderReturnDetails) {
      // 2 筛选出物料编码一致的物料明细
      List<OrderDetail> filterOrderDetails = orderDetailList.stream()
          .filter(orderDetail -> StrUtil.equals(orderDetail.getCode(), cancelOne.getCode()))
          .collect(Collectors.toList());
      if (CollUtil.isEmpty(filterOrderDetails)) {
        throw new ThreadInterruptException(StrUtil.format("【{}】未找到明细，不可取消", cancelOne.getCode()));
      }
      // 计算总数量判断是否超过cancelOne
      BigDecimal totalNum = filterOrderDetails.stream().map(OrderDetail::getNum).reduce(BigDecimal.ZERO, BigDecimal::add);
      if (totalNum.compareTo(cancelOne.getNum()) < 0) {
        throw new ThreadInterruptException(StrUtil.format("【{}】申请数量不可大于商品数量", cancelOne.getCode()));
      }
      filterOrderDetails =
          filterOrderDetails.stream()
              .filter(item -> {
                BigDecimal remainNum =
                    NumberUtil.sub(item.getNum(), item.getShipNum(), item.getCancelNum());
                return remainNum.compareTo(BigDecimal.ZERO) > 0;})
              .collect(Collectors.toList());
      // 3 未找到明细则跳过
      if (CollUtil.isEmpty(filterOrderDetails)) {
        continue;
      }
      // 需要取消的数量
      BigDecimal needCancelNum = cancelOne.getNum();
      while (needCancelNum.compareTo(BigDecimal.ZERO) > 0) {
        // 取第一条明细
        OrderDetail orderDetail = filterOrderDetails.get(0);
        // 4.1 物料明细数量判断是否大于取消数量
        // 剩余可取消数量
        BigDecimal surplusCancelNum =
            NumberUtil.sub(orderDetail.getNum(), orderDetail.getShipNum(), orderDetail.getCancelNum());
        if (surplusCancelNum.compareTo(BigDecimal.ZERO) <= 0) {
          continue;
        }
        // 实际取消
        BigDecimal actualCancelNum = BigDecimal.ZERO;
        if (surplusCancelNum.compareTo(needCancelNum) >= 0) {
          // 4.2 大于则生成取消数量的取消单
          actualCancelNum = needCancelNum;
        } else {
          // 4.3 小于则生成剩余数量的取消单
          actualCancelNum = surplusCancelNum;
        }
        needCancelNum = needCancelNum.subtract(actualCancelNum);
        // 生成取消明细并保存
        OrderCancelDetail orderCancelDetail =
            orderCancelFactory.createDetail(orderCancel, order, orderDetail, actualCancelNum, cancelOne);
        List<OrderCancelDetail> orderCancelDetailCache =
            PreviewContextUtil.getTemporaryListData(ORDER_CANCEL_DETAILS, OrderCancelDetail.class);
        orderCancelDetailCache.add(orderCancelDetail);
        PreviewContextUtil.putTemporaryData(ORDER_CANCEL_DETAILS, orderCancelDetailCache);
        orderCancelDetailRepository.saveAndFlush(orderCancelDetail);
        // 更新取消单总数量
        totalCancelNum = NumberUtil.add(totalCancelNum, actualCancelNum);
        // 更新取消单总金额
        totalCancelPrice = NumberUtil.add(totalCancelPrice, NumberUtil.mul(actualCancelNum, orderDetail.getPrice()));
        // orderDetail更新
        orderDetail.setCancelNum(NumberUtil.add(orderDetail.getCancelNum(), actualCancelNum));
        orderDetail.setUnshipNum(NumberUtil.sub(orderDetail.getNum(), orderDetail.getShipNum(), orderDetail.getCancelNum()));
        orderDetailRepository.saveAndFlush(orderDetail);
        // order 更新
        order = orderCancelFactory.updateOrder(order, orderDetail, orderCancelDetail.getNum());
        orderRepository.saveAndFlush(order);
        // 5 判断本次取消数量是否大于0
        // 5.1 小于等于则退出循环
        if (needCancelNum.compareTo(BigDecimal.ZERO) <= 0) {
          break;
        }
        filterOrderDetails.remove(orderDetail);
        // 5.2 大于则继续从步骤2开始(二次判断)
        if (CollUtil.isEmpty(filterOrderDetails)) {
          break;
        }
      }
      // 更新orderReturnDetails中的数量
      cancelOne.setNum(needCancelNum);
    }
    // 更新金额与数量
    orderCancel.setNum(totalCancelNum);
    orderCancel.setPrice(totalCancelPrice);
    orderCancelRepository.saveAndFlush(orderCancel);
    if (totalCancelNum.compareTo(BigDecimal.ZERO) <= 0) {
      throw new CheckException("取消数量小于等于0，退出创建");
    }
    // 调用SAP 021 标记完成
    if (PreviewContextUtil.isNotPreviewMode()) {
      try {
        this.syncOrderCancel(order, orderCancel);
      } catch (Exception e) {
        log.error("取消单同步SAP失败，异常信息：{}", e.getMessage(), e);
      }
    }
    form.setOrderReturnDetails(orderReturnDetails);
    return orderCancel;
  }

  /**
   * 同步取消单到SAP
   * @param orderCancelId
   */
  @Override
  public void syncOrderCancel(String orderCancelId) {
    OrderCancel orderCancel = orderCancelRepository.findById(orderCancelId)
        .orElseThrow(() -> CheckException.noFindException(OrderCancel.class, orderCancelId));
    Order order = orderRepository.findById(orderCancel.getOrderId())
        .orElseThrow(() -> CheckException.noFindException(Order.class, orderCancelId));
    this.syncOrderCancel(order, orderCancel);
  }

  /**
   * 同步取消单到SAP
   * @param order
   * @param orderCancel
   */
  private void syncOrderCancel(Order order, OrderCancel orderCancel) {
    if (OrderCancelStatus.CANCEL_COMPLETE.getCode().equals(orderCancel.getStatus())) {
      throw new CheckException("取消单已完成，不能重复调用");
    }
    UpdatePurchaseOrderSapParam mm021Param = sapFactory.createMM021ParamForOrderCancel(order, orderCancel);
    if (CollUtil.isNotEmpty(mm021Param.getData().getHead().getItem())) {
      UpdatePurchaseOrderRETURNDTO updatePurchaseOrderRETURNDTO = sapService.sapPurchaseOrderWithAlarm(mm021Param, null);
    }
    orderCancel.setStatus(OrderCancelStatus.CANCEL_COMPLETE.getCode());
    orderCancelRepository.saveAndFlush(orderCancel);
  }

  /**
   * 新建退货单
   * 逻辑变动<br>
   * 1 查询出订单的发货明细<br>
   * 2 筛选出物料编码一致的发货明细<br>
   * 3 未找到明细抛异常<br>
   * 4.1 发货明细判断是否大于退货数量<br>
   * 4.2 大于则生成退货数量的退货单<br>
   * 4.3 小于则生成发货数量的退货单<br>
   * 5.判断本次退货数量是否大于0<br>
   * 5.1 大于则继续从步骤2开始<br>
   * 5.2 小于则进行保存
   * 6 调用sap并更新erpNo
   * v7.1.0版本兼容创建取消单逻辑
   */
  private OrderReturnInfo addOrderRefundRef(OrderReturnAddParam form,
      boolean isSyncErpReturn, boolean sync2Sap) {
    // 记录订单详情退货数量
    List<OrderReturnDetailInfo> orderReturnDetailInfos = new ArrayList<>();
    // 0.查询订单
    // fixme 可能有问题
    Order order = orderRepository.findFirstBySupplierOrderIdAndState(form.getSupplierOrderId(),
        Constants.STATE_OK);
    if (order == null) {
      throw new CheckException("未找到履约单");
    }
    // 1 查询订单的发货明细
    List<DeliveryDetailDTO> orderDeliveryList =
        orderDeliveryService.getDeliveryDetailVOByOrderId(order.getId());
    List<OrderDeliveryDetailDTO> orderDeliveryDetailList =
        orderDeliveryList.stream().map(DeliveryDetailDTO::getDeliveryProductList)
            .flatMap(Collection::stream).collect(Collectors.toList());
    if (CollUtil.isEmpty(orderDeliveryList)) {
      throw new CheckException("未找到发货明细");
    }
    // 生成退货单
    OrderReturn orderReturn = orderReturnFactory.create(order, form);
    orderReturnRepository.saveAndFlush(orderReturn);
    BigDecimal totalReturnNum = BigDecimal.ZERO;
    BigDecimal totalReturnPrice = BigDecimal.ZERO;
    List<OrderReturnDetailAddParam> orderReturnDetails = form.getOrderReturnDetails();
    List<OrderReturnDetailAddParam> filterReturnDetails =
        orderReturnDetails.stream().filter(item -> item.getNum().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.toList());
    for (OrderReturnDetailAddParam needRefundParam : filterReturnDetails) {
      // 2 筛选出物料编码一致的发货明细 并且过滤出剩余可退数量大于0的发货明细
      List<OrderDeliveryDetailDTO> findOrderDeliveryDetailList =
          orderDeliveryDetailList.stream().filter(orderDeliveryDetailDTO -> StrUtil.equals(
              orderDeliveryDetailDTO.getCode(), needRefundParam.getCode()))
              .filter(item -> {
                BigDecimal remainNum = NumberUtil.sub(item.getDelCount(), item.getReturnNum());
                return remainNum.compareTo(BigDecimal.ZERO) > 0;})
              .collect(Collectors.toList());
      // 3 未找到明细抛异常
      if (CollUtil.isEmpty(findOrderDeliveryDetailList)) {
        throw new CheckException(
            String.format("%s 退货数量超出未退数量或发货数量", needRefundParam.getCode()));
      }
      // 需要退货数量
      BigDecimal needRefundNum = needRefundParam.getNum();
      while (needRefundNum.compareTo(BigDecimal.ZERO) > 0) {
        // 取第一条发货明细
        OrderDeliveryDetailDTO orderDeliveryDetail = findOrderDeliveryDetailList.get(0);
        // 获取详情
        OrderDetail orderDetail = orderDetailDao.getOrderDetailByOrderIdAndCode(order.getId(),
            orderDeliveryDetail.getCode());
        // 4.1 发货明细判断是否大于退货数量
        // 剩余可退数量
        BigDecimal surplusReturnNum = NumberUtil.sub(orderDeliveryDetail.getDelCount(), orderDeliveryDetail.getReturnNum());
        if (surplusReturnNum.compareTo(BigDecimal.ZERO) <= 0) {
          continue;
        }
        // 实际退货数量
        BigDecimal actualRefundNum = BigDecimal.ZERO;
        if (surplusReturnNum.compareTo(needRefundNum) >= 0) {
          // 4.2 大于则生成退货数量的退货单
          actualRefundNum = needRefundNum;
        } else {
          // 4.3 小于则生成发货数量的退货单
          actualRefundNum = surplusReturnNum;
        }
        needRefundNum = needRefundNum.subtract(actualRefundNum);
        // todo 为保证旧数据正确，需要额外另外根据orderDetail中的数量进行判断
        // 生成详情并保存
        OrderReturnDetail newOrderReturnDetail =
            orderReturnFactory.createDetail(orderReturn, order, orderDetail, needRefundParam,
                orderDeliveryDetail, actualRefundNum);
        List<OrderReturnDetail> orderReturnDetailCache =
            PreviewContextUtil.getTemporaryListData(ORDER_RETURN_DETAILS, OrderReturnDetail.class);
        orderReturnDetailCache.add(newOrderReturnDetail);
        PreviewContextUtil.putTemporaryData(ORDER_RETURN_DETAILS, orderReturnDetailCache);
        orderReturnDetailRepository.saveAndFlush(newOrderReturnDetail);
        // 更新退货单总数量
        totalReturnNum = NumberUtil.add(totalReturnNum, actualRefundNum);
        // 更新退货单总金额
        totalReturnPrice = NumberUtil.add(totalReturnPrice, NumberUtil.mul(actualRefundNum, orderDetail.getPrice()));
        // orderDeliverDetail更新
        BigDecimal finalActualRefundNum = actualRefundNum;
        orderDeliveryDetail.setReturnNum(NumberUtil.add(orderDeliveryDetail.getReturnNum(), finalActualRefundNum));
        orderDeliveryDetailRepository.findById(orderDeliveryDetail.getId()).ifPresent(
            updateOne -> {
              updateOne.setReturnNum(NumberUtil.add(updateOne.getReturnNum(), finalActualRefundNum));
              orderDeliveryDetailRepository.save(updateOne);
            });
        // orderDetail更新
        orderDetail.setReturnNum(NumberUtil.add(orderDetail.getReturnNum(), actualRefundNum));
        orderDetailRepository.saveAndFlush(orderDetail);
        // order 更新
        order = orderReturnFactory.updateOrder(order, orderDetail, newOrderReturnDetail.getReturnNum());
        orderRepository.saveAndFlush(order);
        // 添加记录
        OrderReturnDetailInfo orderReturnDetailInfo =
            new OrderReturnDetailInfo(orderDetail, newOrderReturnDetail);
        orderReturnDetailInfos.add(orderReturnDetailInfo);
        // 5 判断本次退货数量是否大于0
        // 5.1 小于等于则退出循环
        if (needRefundNum.compareTo(BigDecimal.ZERO) <= 0) {
          break;
        }
        findOrderDeliveryDetailList.remove(orderDeliveryDetail);
        // 5.2 大于则继续从步骤2开始(二次判断)
        if (CollUtil.isEmpty(findOrderDeliveryDetailList)) {
          throw new CheckException(
              String.format("%s 退货数量超出未退数量或发货数量", needRefundParam.getCode()));
        }
      }
    }
    // 更新金额与数量
    orderReturn.setNum(totalReturnNum);
    orderReturn.setPrice(totalReturnPrice);
    if (totalReturnNum.compareTo(BigDecimal.ZERO) <= 0) {
      throw new CheckException("退货数量小于等于0，退出创建");
    }
    orderReturnRepository.saveAndFlush(orderReturn);
    if (PreviewContextUtil.isNotPreviewMode()) {
      // 金蝶调用
      if (OrderErpTypeEnum.KINGDEE_ERP.getDescription().equals(order.getErpType())
          && isSyncErpReturn) {
        syncErpReturn(new HashSet<>(Collections.singleton(orderReturn.getId())));
      }
      // sap异步调用
      if (OrderErpTypeEnum.SAP_ERP.getDescription().equals(order.getErpType())
          && sync2Sap
      ) {
        Order finalOrder = order;
        CompletableFuture<Void> future = CompletableFuture.runAsync(TtlRunnable.get(() -> {
          SyncOrderReturnRes returnRes =
              this.syncOrderReturn(finalOrder, orderReturn, orderReturnDetailInfos, true);
          // 保存erpNo
          orderReturn.setErpNo(returnRes.getProductReturnNo());
          orderReturn.setPurchaseOrderNo(returnRes.getPurchaseOrderNo());
          orderReturnRepository.saveAndFlush(orderReturn);
        }));
      }
    }
    OrderReturnInfo orderReturnInfo = new OrderReturnInfo(orderReturn, orderReturnDetailInfos);
    return orderReturnInfo;
  }

  public void syncErpReturn(Set<String> returnIdList) {
    asyncUtil.execute(returnIdList, (returnIdIds) -> {
      for (String returnId : returnIdIds) {
        try {
          OrderReturn orderReturn = orderReturnTempService.get(returnId,
              () -> CheckException.noFindException(OrderReturn.class, returnId));
          Order order = orderReturn.getOrder();
          if (order == null) {
            throw new CheckException("退货单未获取到关联订单");
          }
          if (StrUtil.isBlank(order.getSaleOrderNo())) {
            return;
          }
          String erpOrderNo = order.getErpOrderNo();
          String erpOrderId = order.getErpOrderId();
          if (StrUtil.isBlank(erpOrderNo) || StrUtil.isBlank(erpOrderId)) {
            throw new CheckException("未获取到订单的采购单号，无法进行入库");
          }
          if (StrUtil.isNotBlank(orderReturn.getErpNo())) {
            throw new CheckException("该退货单已经退料，请勿重复调用！");
          }
          List<RowAndNum> productDetail = new ArrayList<>();
          Map<String, OrderReturnDetail> erpIdMapOrderReturnDetail = new HashMap<>();
          List<OrderReturnDetail> orderReturnDetails =
              orderReturnDetailTempService.getReturnDetailByReturnId(orderReturn.getId());
          for (OrderReturnDetail orderReturnDetail : orderReturnDetails) {
            String erpDetailRowId = Optional.ofNullable(
                    orderDetailDao.getOrderDetailByOrderIdAndCode(order.getId(),
                        orderReturnDetail.getCode())).map(OrderDetail::getErpRowId)
                .orElse(StrUtil.EMPTY);
            if (StrUtil.isNotBlank(erpDetailRowId)) {
              RowAndNum rowAndNum = new RowAndNum();
              rowAndNum.setRowId(Integer.valueOf(erpDetailRowId));
              rowAndNum.setNum(orderReturnDetail.getReturnNum());
              if (Objects.equals(order.getSupplierOpenInvoiceStatus(),
                  Constants.ORDER_INVOICE_STATE_PASS)) {
                rowAndNum.setIsRedTicket(Constants.ERP_OPEN_RED_INVOICE_TYPE_YES);
              }else {
                rowAndNum.setIsRedTicket(Constants.ERP_OPEN_RED_INVOICE_TYPE_NO);
              }
              productDetail.add(rowAndNum);
              erpIdMapOrderReturnDetail.put(erpDetailRowId, orderReturnDetail);
            }
          }
          List<OrderReturnData> orderReturnData =
              erpRequest.pushPurMrb(order.getOrderNo(), erpOrderId, productDetail, StrUtil.EMPTY,
                  StrUtil.EMPTY, StrUtil.EMPTY, true, true);
          if (CollUtil.isNotEmpty(orderReturnData)) {
            // 这里如果 ERP 退料单只有一个则按原退料单处理
            if (orderReturnData.size() == 1) {
              OrderReturnData orderReturnDataOne = orderReturnData.get(0);
              Integer fId = orderReturnDataOne.getFId();
              String fNumber = orderReturnDataOne.getFNumber();
              List<EntryInfoDTO> entryInfo = orderReturnDataOne.getEntryInfo();
              for (EntryInfoDTO entryInfoDTO : entryInfo) {
                Integer rowId = entryInfoDTO.getRowId();
                BigDecimal num = entryInfoDTO.getNum();
                Integer pooRowId = entryInfoDTO.getPooRowId();
                OrderReturnDetail orderReturnDetail =
                    erpIdMapOrderReturnDetail.get(String.valueOf(pooRowId));
                orderReturnDetail.setReturnNum(num);
                orderReturnDetailTempService.save(orderReturnDetail);
              }
              orderReturn.setErpNo(fNumber);
              orderReturnTempService.save(orderReturn);
            }
          } else {
            for (OrderReturnData orderReturnDatum : orderReturnData) {
              List<EntryInfoDTO> entryInfo = orderReturnDatum.getEntryInfo();
              BigDecimal totalReturnNum = BigDecimal.ZERO;
              BigDecimal totalPrice = BigDecimal.ZERO;
              String fNumber = orderReturnDatum.getFNumber();
              List<OrderReturnDetail> orderReturnDetailsNew = new ArrayList<>();
              for (EntryInfoDTO entryInfoDTO : entryInfo) {
                Integer rowId = entryInfoDTO.getRowId();
                BigDecimal num = entryInfoDTO.getNum();
                Integer pooRowId = entryInfoDTO.getPooRowId();
                totalReturnNum = NumberUtil.add(totalReturnNum, num);
                OrderReturnDetail orderReturnDetail =
                    erpIdMapOrderReturnDetail.get(String.valueOf(pooRowId));
                OrderReturnDetail orderReturnDetailNew = MapStructFactory.INSTANCE.toOrderReturnDetail(orderReturnDetail);
                orderReturnDetailNew.setId(null);
                orderReturnDetailNew.setOrderReturn(null);
                orderReturnDetailNew.setReturnNum(num);
                orderReturnDetailsNew.add(orderReturnDetailNew);
                totalPrice =
                    NumberUtil.add(totalPrice, NumberUtil.mul(num, orderReturnDetail.getPrice()));
              }
              OrderReturn orderReturnNew = MapStructFactory.INSTANCE.toOrderReturn(orderReturn);
              orderReturnNew.setId(null);
              orderReturnNew.setPrice(
                  NumberUtil.mul(totalReturnNum, totalPrice.setScale(2, RoundingMode.HALF_UP)));
              long count = orderReturnDao.getOrderReturnCountByOrderIdAndType(order.getId(),
                  Constants_order.ORDER_RETURN_TYPE) + 1;
              //退货单编号
              String returnNo = "TH" + count + "-" + count + order.getOrderNo();
              orderReturnNew.setReturnNo(returnNo);
              orderReturnNew.setErpNo(fNumber);
              OrderReturn save = orderReturnDao.save(orderReturnNew);
              for (OrderReturnDetail orderReturnDetail : orderReturnDetailsNew) {
                orderReturnDetail.setOrderReturn(save);
                orderReturnDetailTempService.save(orderReturnDetail);
              }
            }
            orderReturn.setState(Constants.STATE_DELETE);
          }
          orderReturnTempService.save(orderReturn);
        } catch (Exception e) {
          log.error(ExceptionUtil.stacktraceToString(e, -1));
        }
      }
    });
  }

  @Override
  public MM_075Result syncSapOrderReturn(MM_075Param param, String orderNo) {
    MM_075Result result = null;
    MM_075Result tempResult = new MM_075Result();
    try {
      result = sapService.sapDirectReturnWithError(param, tempResult);
    }catch (Exception e){
      String env = bootConfig.getEnv();
      DingUtils.sendMsgByWarningRobot(
          "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + orderNo +
              "】调用SAP "
              + "ZFM_MM_075退货接口异常，" +
              "，请求参数：" + JSON.toJSONString(param) + "请求结果：" +
              JSON.toJSONString(tempResult)
              + " ，请及时处理！", env);
      if (e instanceof CheckException) {
        throw e;
      }
      log.error("MM075退货异常：" + JSON.toJSONString(tempResult));
      throw new CheckException("SAP系统响应异常，请联系管理员！");
    }
    return result;
  }

  /**
   * 订单履约SRM生成退货单<br>
   * 1.判断订单供应商开票状态
   * 1.1 如果已开票则调用 SAP MM075接口
   * 1.2 如果未开票则调用 SAP MM031接口
   * 2.判断是否成功返回
   * 2.1 成功则记录退ERP退料单号 和 退料单采购订单号
   * 2.2 失败则钉钉群报错
   * @param order
   * @param orderReturn
   * @param returnList
   * @param async
   * @return
   */
  @Override
  public SyncOrderReturnRes syncOrderReturn(Order order, OrderReturn orderReturn, List<OrderReturnDetailInfo> returnList, boolean async) {
    SyncOrderReturnRes res = new SyncOrderReturnRes();
    // 1.判断订单供应商开票状态
    boolean isOpenInvoice =
        Constants.ORDER_INVOICE_STATE_PASS.equals(order.getSupplierOpenInvoiceStatus());
    // 1.1 如果已开票则调用 SAP MM075接口
    if (isOpenInvoice) {
      MM_075Param param = sapFactory.createMM075Param(order, orderReturn, returnList);
      MM_075Result result = null;
      MM_075Result tempResult = new MM_075Result();
      try {
        // 2.判断是否成功返回
        // 2.1 成功则记录退ERP退料单号 和 退料单采购订单号
        result = sapService.sapDirectReturnWithError(param, tempResult);
      } catch (Exception e) {
        // 2.2 失败则钉钉群报错
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + order.getOrderNo() +
                "】调用SAP "
                + "ZFM_MM_075退货接口异常，" +
                "，请求参数：" + JSON.toJSONString(param) + "请求结果：" +
                JSON.toJSONString(tempResult)
                + " ，请及时处理！", env);
        if (e instanceof CheckException) {
          throw e;
        }
        log.error("MM075退货异常：" + JSON.toJSONString(tempResult));
        throw new CheckException("SAP系统响应异常，请联系管理员！");
      }
      if (result != null) {
        String productVoucher = result.getReturnMessages().get(0).getProductVoucher();
        String ebeln = result.getReturnMessages().get(0).getEBELN();
        res.setProductReturnNo(productVoucher);
        res.setPurchaseOrderNo(ebeln);
      }
    }else{
      ReceiptVoucherSynchronizationParam param =
          sapFactory.createMM031OrderReturn(order, orderReturn, returnList);
      ReceiptVoucherSynchronizationResult result = null;
      ReceiptVoucherSynchronizationResult tempResult = new ReceiptVoucherSynchronizationResult();
      try {
        if (async) {
          result = sapService.sapMaterialVoucherWithError(param, tempResult);
        }else{
          result = sapService.sapMaterialVoucherWithLockGroup(param, tempResult);
        }
      } catch (Exception e) {
        // 2.2 失败则钉钉群报错
        String env = bootConfig.getEnv();
        DingUtils.sendMsgByWarningRobot(
            "【" + env + "环境 " + bootConfig.getAppName() + "】 【" + order.getOrderNo() +
                "】调用SAP "
                + "ZFM_MM_031退货接口异常，" +
                "，请求参数：" + JSON.toJSONString(param) + "请求结果：" +
                JSON.toJSONString(tempResult)
                + " ，请及时处理！", env);
        if (e instanceof CheckException) {
          throw e;
        }
        log.error("MM031退货异常：" + JSON.toJSONString(result));
        throw new CheckException("SAP系统响应异常，请联系管理员！");
      }
      if (result != null) {
        String productVoucher = result.getReturnMessages().get(0).getDocumentNumber();
        String purchaseOrderNo = order.getErpOrderNo();
        res.setProductReturnNo(productVoucher);
        res.setPurchaseOrderNo(purchaseOrderNo);
      }
    }
    return res;
  }
}
