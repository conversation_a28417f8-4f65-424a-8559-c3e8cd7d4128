package com.xhgj.srm.api.config;

import com.xhgj.srm.api.domain.SrmUserDetails;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.jpa.entity.User;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/4/18 20:42
 */
@Component
@Slf4j
public class SrmManageAuditor implements AuditorAware<String> {

  @Autowired private ManageSecurityUtil manageSecurityUtil;

  @Override
  public Optional<String> getCurrentAuditor() {
    return Optional.ofNullable(manageSecurityUtil.getSrmUserDetails())
        .map(SrmUserDetails::getUser)
        .map(User::getId);
  }
}
