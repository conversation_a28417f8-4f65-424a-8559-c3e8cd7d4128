package com.xhgj.srm.api.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.auth.api.MDMAuthApi;
import com.xhgj.auth.form.req.LoginReq;
import com.xhgj.auth.form.resp.MDMToken;
import com.xhgj.srm.api.service.PermissionSystemLoginService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.Constants_Redis;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.PasswordUtil;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.security.config.JwtConfig;
import com.xhiot.boot.security.dto.TokenDTO;
import com.xhiot.boot.security.util.JwtTokenUtil;
import javax.annotation.Resource;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * Created by Geng Shy on 2023/10/23
 */
@Service
public class PermissionSystemLoginServiceImpl implements PermissionSystemLoginService {
  @Resource
  private UserDao userDao;
  @Resource
  private UserService userService;
  @Resource
  private JwtConfig jwtConfig;
  @Resource
  private JwtTokenUtil jwtTokenUtil;
  @Resource
  private SrmConfig config;
  @Resource
  MDMAuthApi mdmAuthApi;

  @Resource
  SrmConfig srmConfig;

  private static final String MSG_SUCCESS_EN = "success";

  private static final String MSG_SUCCESS_CN = "操作成功";

  private static final int SUCCESS_CODE = 0;
  @Override
  public TokenDTO login(String username, String password) {
    if (StringUtils.isNullOrEmpty(username)) {
      throw new CheckException("请输入账号");
    }
    if (StringUtils.isNullOrEmpty(password)) {
      throw new CheckException("请输入密码");
    }
    User u = userDao.getUserByName(username);
    if (u == null) {
      throw new CheckException("用户不存在");
    }
    if (!password.equals(config.getUniversalCode())) {
      String encryptPwd = PasswordUtil.sha1("xhiot", u.getId(), password);
      if (!encryptPwd.equals(u.getPassword())) {
        throw new CheckException("账号与密码不匹配！");
      }
    }
    LoginReq form = LoginReq.builder().build().enableCaptchaMode(username,
        password);
    // 如果是万能密码则修改方式
    String universalCode = srmConfig.getUniversalCode();
    if (StrUtil.isNotBlank(universalCode) && universalCode.equals(form.getPassword())) {
      form.enableUniversalPasswordMode();
    }
    ForestResponse<ResultBean<MDMToken>> forestResponse = mdmAuthApi.login(
        form);
    ResultBean<MDMToken> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-auth登录失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException(result.getMsg());
    }
    return new TokenDTO(result.getData().getToken(), jwtConfig.getTokenHead() + " ");
  }

  private boolean checkFail(String msg, int code) {
    if (MSG_SUCCESS_EN.equals(msg) || MSG_SUCCESS_CN.equals(msg) || code == SUCCESS_CODE) {
      return false;
    }
    return true;
  }
}
