spring:
  application:
    name: srm-mission-consumer
  profiles:
    active: env-test
    include: datasource,boot
  http:
    encoding:
      charset: utf-8
      enabled: true
      force: true
  servlet:
    multipart:
      max-file-size: 20MB
  rabbitmq:
    listener:
      simple:
        prefetch: 1
        acknowledge-mode: manual
      direct:
        acknowledge-mode: manual
    connection-timeout: 0s
    publisher-confirms: true
    publisher-returns: true
  cloud:
    nacos:
      config:
        file-extension: "yaml"
        shared-configs:
          - data-id: "srm-shared-configs.yaml"
            refresh: true
server:
  port:
  servlet:
    context-path: /
  tomcat:
    uri-encoding: utf-8
    max-threads: 1000
    min-spare-threads: 30
#logging:
#  config: classpath:log4j2-linux.yml
xhiot:
  boot:
    app-name: ${spring.application.name}
    env: "prod"
    sys:
      error:
        ding:
          config:
            access-token: "3c0c25fd94f37f3c7b19ab884d1a39148b4d5c5ed6dfe2a54e57b27baf2e651a"
            at-mobiles-or-user-ids: ["13197800218"]