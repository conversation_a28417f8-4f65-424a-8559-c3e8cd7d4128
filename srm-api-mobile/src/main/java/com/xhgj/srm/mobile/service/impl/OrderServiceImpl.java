package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.mall.express.support.dto.LogisticsInfoDTO;
import com.xhgj.mall.express.support.param.OrcParam;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.constants.SupplierConstants;
import com.xhgj.srm.common.dto.ExpressCompanyDTO;
import com.xhgj.srm.common.dto.OrderPlatformDTO;
import com.xhgj.srm.common.dto.PaymentStatus;
import com.xhgj.srm.common.enums.OrderDeliveryProgress;
import com.xhgj.srm.common.enums.OrderErpTypeEnum;
import com.xhgj.srm.common.enums.PlatformMapContactMobileEnums;
import com.xhgj.srm.common.enums.PurchaseApplicationTypeEnum;
import com.xhgj.srm.common.enums.TitleOfTheContractEnum;
import com.xhgj.srm.common.enums.order.OrderReceiptPaymentMethodEnum;
import com.xhgj.srm.common.enums.supplierorder.OrderPageSortType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.CommonlyUseUtil;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.SAPToolUtils;
import com.xhgj.srm.common.utils.WordUtil;
import com.xhgj.srm.dto.order.AllowPaymentOrderDTO;
import com.xhgj.srm.dto.order.OrderDeliveryDetailDTO;
import com.xhgj.srm.dto.order.OrderDetailInvoiceDTO;
import com.xhgj.srm.dto.order.param.AllowPaymentOrderQueryParam;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.OrderDeliveryDao;
import com.xhgj.srm.jpa.dao.OrderDeliveryDetailDao;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceDao;
import com.xhgj.srm.jpa.dao.OrderInvoiceToOrderDao;
import com.xhgj.srm.jpa.dao.OrderReturnDetailDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderAccept;
import com.xhgj.srm.jpa.entity.OrderDelivery;
import com.xhgj.srm.jpa.entity.OrderDeliveryDetail;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhgj.srm.jpa.entity.OrderInvoice;
import com.xhgj.srm.jpa.entity.OrderInvoiceToOrder;
import com.xhgj.srm.jpa.entity.OrderOpenInvoice;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.OrderDeliveryDetailRepository;
import com.xhgj.srm.jpa.repository.OrderDeliveryRepository;
import com.xhgj.srm.jpa.repository.OrderDetailRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentDao;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhgj.srm.mobile.dto.OrderInvoiceFileDTO;
import com.xhgj.srm.mobile.dto.SupplierOrderSchemeDTO;
import com.xhgj.srm.mobile.dto.order.BaseOrderInvoiceDTO;
import com.xhgj.srm.mobile.dto.order.BatchOrderInvoiceParams;
import com.xhgj.srm.mobile.dto.order.CustomerPaybackDTO;
import com.xhgj.srm.mobile.dto.order.CustomerPaybackParams;
import com.xhgj.srm.mobile.dto.order.DeliveryOrderDetailDTO;
import com.xhgj.srm.mobile.dto.order.DeliveryParamDTO;
import com.xhgj.srm.mobile.dto.order.DeliveryProductParamDTO;
import com.xhgj.srm.mobile.dto.order.InvoiceDTO;
import com.xhgj.srm.mobile.dto.order.MobileOrderTableDTO;
import com.xhgj.srm.mobile.dto.order.OpenInvoiceDTO;
import com.xhgj.srm.mobile.dto.order.OrderInvoiceInfoDTO;
import com.xhgj.srm.mobile.dto.order.OrderInvoicePageQuery;
import com.xhgj.srm.mobile.dto.order.OrderInvoiceParams;
import com.xhgj.srm.mobile.dto.order.OrderSomeStatusDTO;
import com.xhgj.srm.mobile.dto.order.OrderTableDTO;
import com.xhgj.srm.mobile.dto.order.SubmitOrderAcceptDTO;
import com.xhgj.srm.mobile.dto.order.SubmitOrderAcceptDTO.DataInfo;
import com.xhgj.srm.mobile.dto.order.SupplierOrderTableDTO;
import com.xhgj.srm.mobile.dto.order.delivery.GetCountDTO;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryCountDTO;
import com.xhgj.srm.mobile.dto.order.delivery.OrderDeliveryPageDTO;
import com.xhgj.srm.mobile.dto.order.delivery.SupplierOrderDeliveryDTO;
import com.xhgj.srm.mobile.dto.order.delivery.query.DeliveryOrderQueryParam;
import com.xhgj.srm.mobile.dto.order.delivery.query.SupplierOrderDeliveryQueryParam;
import com.xhgj.srm.mobile.dto.order.logistics.LogisticsInformationParam;
import com.xhgj.srm.mobile.dto.order.param.OrderPageQueryParam;
import com.xhgj.srm.mobile.dto.order.vo.OrderCountVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderDetailVO;
import com.xhgj.srm.mobile.dto.order.vo.OrderPageVO;
import com.xhgj.srm.mobile.enums.OrderType;
import com.xhgj.srm.mobile.factory.MapStructFactory;
import com.xhgj.srm.mobile.service.FileService;
import com.xhgj.srm.mobile.service.OrderAccountToOrderService;
import com.xhgj.srm.mobile.service.OrderDetailService;
import com.xhgj.srm.mobile.service.OrderService;
import com.xhgj.srm.mobile.service.SearchSchemeService;
import com.xhgj.srm.mobile.service.SupplierOrderService;
import com.xhgj.srm.mobile.service.SupplierPerformanceService;
import com.xhgj.srm.mobile.service.SupplierUserService;
import com.xhgj.srm.mobile.service.UserService;
import com.xhgj.srm.mobile.utils.OrderUtil;
import com.xhgj.srm.request.config.ERPConfig;
import com.xhgj.srm.request.dto.dock.DownloadDeliveryDocumentForm;
import com.xhgj.srm.request.dto.erp.BaseReceivableReturnDTO;
import com.xhgj.srm.request.dto.erp.ERPOrderPayAmountDTO;
import com.xhgj.srm.request.dto.erp.ReceivableBillDTO;
import com.xhgj.srm.request.dto.erp.ReceivableQueryDTO;
import com.xhgj.srm.request.dto.erp.ReceivableQueryDTO.Model;
import com.xhgj.srm.request.dto.erp.ReceivableReturnDTO;
import com.xhgj.srm.request.dto.erp.RowAndQty;
import com.xhgj.srm.request.dto.erp.UpdateErpOrderRateParams;
import com.xhgj.srm.request.dto.erp.UpdateErpOrderRateParams.OrderData;
import com.xhgj.srm.request.dto.erp.UpdateErpOrderRateParams.OrderData.Entry;
import com.xhgj.srm.request.service.third.dock.DockService;
import com.xhgj.srm.request.service.third.erp.ERPRequest;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam.Item;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult.ReturnMessage;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam.UpdatePurchaseOrderDATADTO.UpdatePurchaseOrderHEADDTO.ITEMDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapResult.UpdatePurchaseOrderRETURNDTO;
import com.xhgj.srm.request.service.third.mpm.impl.MPMServiceExtImpl;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.request.vo.dock.FileByteAndType;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhgj.srm.service.OrderDeliveryService;
import com.xhgj.srm.service.OrderInvoiceService;
import com.xhgj.srm.service.OrderInvoiceTemplateService;
import com.xhgj.srm.service.OrderNeedPaymentService;
import com.xhgj.srm.service.OrderOpenInvoiceService;
import com.xhgj.srm.service.OrderReceiptRecordService;
import com.xhgj.srm.service.ShareOrderService;
import com.xhgj.srm.service.SharePlatformService;
import com.xhgj.srm.util.PlatformUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.AsyncUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

  private final String url;
  @Autowired private OrderRepository repository;
  @Autowired private OrderDeliveryRepository orderDeliveryRepository;
  @Autowired private OrderDeliveryDetailRepository orderDeliveryDetailRepository;
  @Autowired private OrderDao orderDao;
  @Autowired private SupplierOrderService supplierOrderService;
  @Autowired private OrderPaymentDao orderPaymentDao;
  @Autowired private FileDao fileDao;
  @Autowired private OrderDetailDao orderDetailDao;
  @Autowired private OrderDeliveryDao orderDeliveryDao;
  @Autowired private OrderReturnDetailDao orderReturnDetailDao;
  @Autowired private OrderDetailRepository orderDetailRepository;
  @Autowired private SearchSchemeDao searchSchemeDao;
  @Autowired private OrderDeliveryDetailDao orderDeliveryDetailDao;
  @Autowired private HttpUtil httpUtil;
  @Autowired private ExportUtil exportUtil;
  @Autowired private OrderInvoiceService orderInvoiceService;
  @Autowired private FileService fileService;
  @Autowired private OrderAcceptService orderAcceptService;
  @Autowired private OrderOpenInvoiceService orderOpenInvoiceService;
  @Autowired private OrderAccountToOrderService orderAccountToOrderService;
  @Autowired private SearchSchemeService searchSchemeService;
  @Autowired private SupplierUserService supplierUserService;
  @Autowired private OrderInvoiceToOrderDao orderInvoiceToOrderDao;
  @Autowired private OrderInvoiceDao orderInvoiceDao;
  @Autowired private OrderDetailService orderDetailService;
  @Autowired FileRepository fileRepository;
  @Autowired private OrderInvoiceTemplateService orderInvoiceTemplateService;
  @Autowired private AsyncUtil asyncUtil;
  @Autowired private OrderDeliveryService orderDeliveryService;
  @Autowired private ERPRequest erpRequest;
  @Autowired private SupplierPerformanceService supplierPerformanceService;
  @Autowired private PlatformTransactionManager platformTransactionManager;
  @Resource private SharePlatformService platformService;
  @Autowired private ERPConfig erpConfig;
  @Resource private SupplierInGroupRepository supplierInGroupRepository;
  @Resource private RedissonClient redissonClient;
  @Resource private UserService userService;
  @Resource private OrderService orderService;
  @Resource
  private DockService dockService;
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  private OrderNeedPaymentService orderNeedPaymentService;
  // 2023-06-19 22:00:00
  private final Long time = 1687183200000L;

  // 通过
  private final int PASS = 1;
  // 异常
  private final int UNPASS = 2;

  /** 咸亨送货单模板地址 */
  private static final String DELIVERY_NOTE_URL = "srm/order/5.9.3项目验收单.docx";

  /** 中国电建平台验收单 */
  private static final String DELIVERY_NOTE_URL_TYPE_ZGDJ = "srm/order/5.9.3电建项目验收单.docx";

  /** 能建商城平台验收单 */
  private static final String DELIVERY_NOTE_URL_TYPE_NJSC = "srm/order/5.9.3项目验收单.docx";

  public OrderServiceImpl(SrmConfig config) {
    this.url = config.getUploadUrl();
  }

  @Override
  public BootBaseRepository<Order, String> getRepository() {
    return repository;
  }

  @Resource
  SAPService sapService;
  @Resource
  ShareOrderService shareOrderService;
  @Resource
  MPMServiceExtImpl mpmServiceExt;
  @Resource
  OMSService omsService;

  @Autowired private OrderReceiptRecordService orderReceiptRecordService;
  @Resource
  private SrmConfig srmConfig;
  @Override
  public OrderCountVO getOrderStateAndCount(String supplierId) {
    OrderCountVO vo = new OrderCountVO();
    long waitCount =
        orderDao.getOrderCountBySupplierIdAndType(supplierId, Constants_order.ORDER_STATE_WAIT);
    long processCount =
        orderDao.getOrderCountBySupplierIdAndType(supplierId, Constants_order.ORDER_STATE_PROCESS);
    long acceptingCount =
        orderDao.getOrderCountBySupplierIdAndType(
            supplierId, Constants_order.ORDER_STATE_ACCEPTING);
    long completeCount =
        orderDao.getOrderCountBySupplierIdAndType(
            supplierId, Constants_order.ORDER_SEARCH_STATE_ALL);
    List<String> orderPageStates = ListUtil.toList(Constants_order.ORDER_STATE_ACCEPTING);
    long rejectCount =
        orderDao.getOrderCountBySupplierIdAndSignType(
            supplierId, Constants_order.ORDER_ACCEPT_REJECT,orderPageStates);
    long toBeUploadedCount =
        orderDao.getOrderCountBySupplierIdAndSignType(
            supplierId, Constants_order.ORDER_ACCEPT_PENDING_UPLOADING,orderPageStates);
    long reviewCount =
        orderDao.getOrderCountBySupplierIdAndSignType(
            supplierId, Constants_order.ORDER_ACCEPT_PENDING_AUDITING,orderPageStates);
    long dropShippingCount = waitCount + processCount;
    vo.setWaitCount(waitCount);
    vo.setProcessCount(processCount);
    vo.setAcceptedCount(acceptingCount);
    vo.setCompleteCount(completeCount);
    vo.setDropShippingCount(dropShippingCount);
    vo.setRejectCount(rejectCount);
    vo.setToBeUploadedCount(toBeUploadedCount);
    vo.setReviewCount(reviewCount);
    return vo;
  }

  @Override
  @Transactional
  public PageResult<OrderPageVO> getOrderPage(OrderPageQueryParam orderPageQuery) {
    if (StringUtils.isNullOrEmpty(orderPageQuery.getSchemeId())) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(
              orderPageQuery.getUserId(), Constants.SEARCH_TYPE_SUPPLIER_ORDER);
      if (search != null) {
        orderPageQuery.setSchemeId(search.getId());
      }
    }
    if (StrUtil.isNotBlank(orderPageQuery.getSchemeId())) {
      SearchScheme search = searchSchemeDao.get(orderPageQuery.getSchemeId());
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          orderPageQuery.setOrderNo(
              StrUtil.blankToDefault(
                  orderPageQuery.getOrderNo(),
                  searchJo.containsKey("orderNo") ? searchJo.getString("orderNo") : ""));
          orderPageQuery.setCustomer(
              StrUtil.blankToDefault(
                  orderPageQuery.getCustomer(),
                  searchJo.containsKey("customer") ? searchJo.getString("customer") : ""));
          orderPageQuery.setPlatform(
              StrUtil.blankToDefault(
                  orderPageQuery.getPlatform(),
                  searchJo.containsKey("platform") ? searchJo.getString("platform") : ""));
          orderPageQuery.setConsignee(
              StrUtil.blankToDefault(
                  orderPageQuery.getConsignee(),
                  searchJo.containsKey("consignee") ? searchJo.getString("consignee") : ""));
          orderPageQuery.setMobile(
              StrUtil.blankToDefault(
                  orderPageQuery.getMobile(),
                  searchJo.containsKey("mobile") ? searchJo.getString("mobile") : ""));
          orderPageQuery.setAddress(
              StrUtil.blankToDefault(
                  orderPageQuery.getAddress(),
                  searchJo.containsKey("address") ? searchJo.getString("address") : ""));
          orderPageQuery.setOrderState(
              StrUtil.blankToDefault(
                  orderPageQuery.getOrderState(),
                  searchJo.containsKey("orderState") ? searchJo.getString("orderState") : ""));
          orderPageQuery.setOrderPageState(
              StrUtil.blankToDefault(
                  orderPageQuery.getOrderPageState(),
                  searchJo.containsKey("orderPageState")
                      ? searchJo.getString("orderPageState")
                      : ""));
          orderPageQuery.setInvoicingState(
              StrUtil.blankToDefault(
                  orderPageQuery.getInvoicingState(),
                  searchJo.containsKey("invoicingState")
                      ? searchJo.getString("invoicingState")
                      : ""));
          orderPageQuery.setStartDate(
              StrUtil.blankToDefault(
                  orderPageQuery.getStartDate(),
                  searchJo.containsKey("startDate") ? searchJo.getString("startDate") : ""));
          orderPageQuery.setEndDate(
              StrUtil.blankToDefault(
                  orderPageQuery.getEndDate(),
                  searchJo.containsKey("endDate") ? searchJo.getString("endDate") : ""));
          orderPageQuery.setSignVoucher(
              StrUtil.blankToDefault(
                  orderPageQuery.getSignVoucher(),
                  searchJo.containsKey("signVoucher") ? searchJo.getString("signVoucher") : ""));
          orderPageQuery.setCustomerPayback(
              StrUtil.blankToDefault(
                  orderPageQuery.getCustomerPayback(),
                  searchJo.containsKey("customerPayback")
                      ? searchJo.getString("customerPayback")
                      : ""));
          orderPageQuery.setAccountOpenInvoiceStatus(
              StrUtil.blankToDefault(
                  orderPageQuery.getAccountOpenInvoiceStatus(),
                  searchJo.containsKey("accountOpenInvoiceStatus")
                      ? searchJo.getString("accountOpenInvoiceStatus")
                      : ""));
          orderPageQuery.setAccountStatus(
              StrUtil.blankToDefault(
                  orderPageQuery.getAccountStatus(),
                  searchJo.containsKey("accountStatus")
                      ? searchJo.getString("accountStatus")
                      : ""));
          orderPageQuery.setPaymentStatus(
              StrUtil.blankToDefault(
                  orderPageQuery.getPaymentStatus(),
                  searchJo.containsKey("paymentStatus")
                      ? searchJo.getString("paymentStatus")
                      : ""));
          orderPageQuery.setSalesReturnState(
              ObjectUtil.defaultIfNull(
                  orderPageQuery.getSalesReturnState(),
                  searchJo.containsKey("salesReturnState")
                      ? Boolean.valueOf(searchJo.getString("salesReturnState"))
                      : null));
          orderPageQuery.setStartWriteOffTime(
              ObjectUtil.defaultIfNull(
                  orderPageQuery.getStartWriteOffTime(),
                  searchJo.containsKey("startWriteOffTime")
                      ? searchJo.getString("startWriteOffTime")
                      : ""));
          orderPageQuery.setEndWriteOffTime(
              ObjectUtil.defaultIfNull(
                  orderPageQuery.getEndWriteOffTime(),
                  searchJo.containsKey("endWriteOffTime")
                      ? searchJo.getString("endWriteOffTime")
                      : ""));
          orderPageQuery.setPaymentTypeName(
              StrUtil.blankToDefault(
                  orderPageQuery.getPaymentTypeName(),
                  searchJo.containsKey("paymentTypeName")
                      ? searchJo.getString("paymentTypeName")
                      : ""));
        }
      }
    }
    //查询履约中的订单
    List<String> orderPageStates = ListUtil.toList(Constants_order.ORDER_STATE_ACCEPTING);
    if (StrUtil.isNotBlank(orderPageQuery.getOrderPageState())) {
      if (Constants_order.ORDER_SEARCH_STATE_ALL.equals(orderPageQuery.getOrderPageState())) {
        orderPageStates.add(Constants_order.ORDER_STATE_ACCEPTED);
        orderPageStates.add(Constants_order.ORDER_STATE_RETURN);
        orderPageStates.add(Constants_order.ORDER_STATE_CANCEL);
        orderPageStates.add(Constants_order.ORDER_STATE_WITHDRAW);
      }
      // 页面订单状态为5，查询待履约和履约中的订单
      if (Constants_order.ORDER_SEARCH_STATE_PENDING_AND_IN_PROGRESS.equals(
          orderPageQuery.getOrderPageState())) {
        orderPageStates.add(Constants_order.ORDER_STATE_WAIT);
        orderPageStates.add(Constants_order.ORDER_STATE_PROCESS);
      }
      orderPageStates.add(orderPageQuery.getOrderPageState());
    }
    List<String> customerReturnList = new ArrayList<>();
    if (!StringUtils.isNullOrEmpty(orderPageQuery.getCustomerPayback())) {
      if (orderPageQuery.getCustomerPayback().equals(Constants_order.RETURN_PROGRESS_PART)) {
        customerReturnList.add(Constants_order.RETURN_PROGRESS_NO);
        customerReturnList.add(Constants_order.RETURN_PROGRESS_PART);
      } else {
        customerReturnList.add(Constants_order.RETURN_PROGRESS_ALL);
      }
    }
    // 有退货条件为false是置空
    if (orderPageQuery.getSalesReturnState() != null && !orderPageQuery.getSalesReturnState()) {
      orderPageQuery.setSalesReturnState(null);
    }
    //驳回排序，审核中排序，待上传排序
    String signVoucher = orderPageQuery.getSignVoucher();
    Page<Order> page =
        orderDao.getOrderPageBySupplier(
            orderPageQuery.getSupplierId(),
            orderPageQuery.getOrderNo(),
            orderPageQuery.getCustomer(),
            orderPageQuery.getOrderState(),
            orderPageQuery.getConsignee(),
            orderPageQuery.getMobile(),
            orderPageQuery.getAddress(),
            orderPageStates,
            orderPageQuery.getInvoicingState(),
            orderPageQuery.getStartDate(),
            orderPageQuery.getEndDate(),
            orderPageQuery.getPlatform(),
            orderPageQuery.getSignVoucher(),
            customerReturnList,
            orderPageQuery.getPageNo(),
            orderPageQuery.getPageSize(),
            orderPageQuery.getAccountStatus(),
            orderPageQuery.getAccountOpenInvoiceStatus(),
            Constants_order.ORDER_STATE_WITHDRAW,
            orderPageQuery.getPaymentStatus(),
            orderPageQuery.getSalesReturnState(),
            orderPageQuery.getStartWriteOffTime(),
            orderPageQuery.getEndWriteOffTime(),
            orderPageQuery.getPaymentTypeName(),
            OrderPageSortType.getByCode(signVoucher)
            );
    List<OrderPageVO> pageDataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (orderPageQuery.getPageNo() <= totalPages) {
      List<Order> orderList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (Order order : orderList) {
        String name = platformService.findNameByCode(order.getType());
        OrderPageVO data = new OrderPageVO(order, name);
        // 签收凭证
        String acceptState = orderAcceptService.getAcceptState(order.getId());
        data.setSignVoucherState(acceptState);
        boolean isSign =
            Objects.equals(order.getOrderState(), Constants_order.ORDER_STATE_ACCEPTING)
                && Objects.equals(acceptState, Constants_order.ORDER_ACCEPT_CONSENT);
        data.setRedSport(isSign);
        Object[] count = orderDetailDao.getReturnCountAndShipCountAndSkuNumCount(order.getId());
        // 已发数量
        BigDecimal totalReturnNum = (BigDecimal) count[0];
        BigDecimal totalShipNum = (BigDecimal) count[1];
        BigDecimal totalNum = (BigDecimal) count[2];
        // 取消数量
        BigDecimal totalCancelNum = (BigDecimal) count[3];
        if (totalNum.compareTo(BigDecimal.ZERO) > 0) {
          data.setProgress(
              NumberUtil.sub(totalShipNum, totalReturnNum)
                  + "/"
                  + NumberUtil.sub(totalNum, totalReturnNum, totalCancelNum));
        } else {
          data.setProgress("-");
        }
        pageDataList.add(data);
      }
    }
    return new PageResult<>(
        pageDataList,
        page.getTotalElements(),
        totalPages,
        orderPageQuery.getPageNo(),
        orderPageQuery.getPageSize());
  }

  @Override
  public <T extends BaseOrderInvoiceDTO> PageResult<T> getPermitOrderInvoicePage(
      Class<T> cls,
      OrderInvoicePageQuery orderInvoicePageQuery,
      boolean excludeNoInvoiceOrder,
      String supplierId) {
    String schemeId = orderInvoicePageQuery.getSchemeId();
    String orderNo = orderInvoicePageQuery.getOrderNo();
    String orderState = orderInvoicePageQuery.getOrderState();
    String platform = orderInvoicePageQuery.getPlatform();
    String price = orderInvoicePageQuery.getPrice();
    String customer = orderInvoicePageQuery.getCustomer();
    String orderStartTime = orderInvoicePageQuery.getOrderStartTime();
    String beginTime = orderInvoicePageQuery.getOrderStartTime();
    String endTime = orderInvoicePageQuery.getOrderEndTime();
    if (!ObjectUtils.isEmpty(beginTime)) {
      orderStartTime = null;
    }
    List<String> invoicingState = orderInvoicePageQuery.getInvoicingState();
    String type = orderInvoicePageQuery.getInvoiceType();
    String title = orderInvoicePageQuery.getTitle();
    String excludeInvoicingState = excludeNoInvoiceOrder ? Constants_order.INVOICE_STATE_UN : null;
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(
              supplierId, Constants.SEARCH_TYPE_ORDER_INVOICE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        SupplierOrderSchemeDTO supplierOrderSchemeDTO =
            JSON.parseObject(search.getContent(), new TypeReference<SupplierOrderSchemeDTO>() {});
        if (supplierOrderSchemeDTO != null) {
          orderNo = StrUtil.blankToDefault(orderNo, supplierOrderSchemeDTO.getOrderNo());
          orderState = StrUtil.blankToDefault(orderState, supplierOrderSchemeDTO.getOrderState());
          platform = StrUtil.blankToDefault(platform, supplierOrderSchemeDTO.getPlatform());
          price = StrUtil.blankToDefault(price, supplierOrderSchemeDTO.getPrice());
          customer = StrUtil.blankToDefault(customer, supplierOrderSchemeDTO.getCustomer());
          orderStartTime =
              StrUtil.blankToDefault(orderStartTime, supplierOrderSchemeDTO.getOrderStartTime());
          beginTime = StrUtil.blankToDefault(beginTime, supplierOrderSchemeDTO.getBeginTime());
          endTime = StrUtil.blankToDefault(endTime, supplierOrderSchemeDTO.getEndTime());
          invoicingState =
              CollUtil.defaultIfEmpty(invoicingState, supplierOrderSchemeDTO.getInvoicingState());
          type = StrUtil.blankToDefault(type, supplierOrderSchemeDTO.getType());
          title = StrUtil.blankToDefault(title, supplierOrderSchemeDTO.getTitle());
          excludeInvoicingState =
              StrUtil.blankToDefault(
                  excludeInvoicingState, supplierOrderSchemeDTO.getExcludeInvoicingState());
        }
      }
    }
    Page<Object[]> orderInvoicePage =
        orderDao.getOrderInvoiceAndStatePage(
            orderNo,
            orderState,
            platform,
            price,
            customer,
            orderStartTime,
            beginTime,
            endTime,
            invoicingState,
            type,
            title,
            excludeInvoicingState,
            supplierId,
            orderInvoicePageQuery.getEnterPriseName(),
            null, null,
            orderInvoicePageQuery.getPageNo(),
            orderInvoicePageQuery.getPageSize());
    return PageResultBuilder.buildPageResult(
        orderInvoicePage,
        (objects -> {
          T dto = ReflectUtil.newInstance(cls);
          dto.setId(String.valueOf(ObjectUtil.defaultIfNull(objects[0], "")));
          dto.setOrderNo(String.valueOf(ObjectUtil.defaultIfNull(objects[1], "")));
          dto.setPlatform(String.valueOf(ObjectUtil.defaultIfNull(objects[2], "")));
          dto.setPrice(
              new BigDecimal(
                  String.valueOf(ObjectUtil.defaultIfNull(objects[3], BigDecimal.ZERO))));
          if (dto instanceof InvoiceDTO) {
            ((InvoiceDTO) dto).setType(String.valueOf(ObjectUtil.defaultIfNull(objects[4], "")));
            ((InvoiceDTO) dto).setTitle(String.valueOf(ObjectUtil.defaultIfNull(objects[5], "")));
          }
          dto.setEnterPriseName(String.valueOf(ObjectUtil.defaultIfNull(objects[6], "")));
          dto.setApplicationTime(String.valueOf(ObjectUtil.defaultIfNull(objects[7], "-")));
          dto.setInvoicingState(String.valueOf(ObjectUtil.defaultIfNull(objects[8], "")));
          dto.setCustomer(String.valueOf(ObjectUtil.defaultIfNull(objects[10], "")));
          dto.setCreateTime(String.valueOf(ObjectUtil.defaultIfNull(objects[11], "-")));
          return dto;
        }));
  }

  @Override
  public <T extends BaseOrderInvoiceDTO> PageResult<T> getOrderInvoicePage(
      Class<T> cls,
      OrderInvoicePageQuery orderInvoicePageQuery,
      boolean excludeNoInvoiceOrder,
      String supplierId) {
    String schemeId = orderInvoicePageQuery.getSchemeId();
    String orderNo = orderInvoicePageQuery.getOrderNo();
    String orderState = orderInvoicePageQuery.getOrderState();
    String platform = orderInvoicePageQuery.getPlatform();
    String price = orderInvoicePageQuery.getPrice();
    String customer = orderInvoicePageQuery.getEnterPriseName();
    String orderStartTime = orderInvoicePageQuery.getOrderStartTime();
    String beginTime = orderInvoicePageQuery.getCreateTimeBegin();
    String endTime = orderInvoicePageQuery.getCreateTimeEnd();
    List<String> invoicingState = orderInvoicePageQuery.getInvoicingState();
    String type = orderInvoicePageQuery.getInvoiceType();
    String title = orderInvoicePageQuery.getTitle();
    String invoiceApplicationNum = orderInvoicePageQuery.getInvoiceApplicationNum();
    String excludeInvoicingState = excludeNoInvoiceOrder ? Constants_order.INVOICE_STATE_UN : null;
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(
              supplierId, Constants.SEARCH_TYPE_ORDER_INVOICE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        SupplierOrderSchemeDTO supplierOrderSchemeDTO =
            JSON.parseObject(search.getContent(), new TypeReference<SupplierOrderSchemeDTO>() {});
        if (supplierOrderSchemeDTO != null) {
          orderNo = StrUtil.blankToDefault(orderNo, supplierOrderSchemeDTO.getOrderNo());
          orderState = StrUtil.blankToDefault(orderState, supplierOrderSchemeDTO.getOrderState());
          platform = StrUtil.blankToDefault(platform, supplierOrderSchemeDTO.getPlatform());
          price = StrUtil.blankToDefault(price, supplierOrderSchemeDTO.getPrice());
          orderStartTime =
              StrUtil.blankToDefault(orderStartTime, supplierOrderSchemeDTO.getOrderStartTime());
          beginTime = StrUtil.blankToDefault(beginTime, supplierOrderSchemeDTO.getBeginTime());
          endTime = StrUtil.blankToDefault(endTime, supplierOrderSchemeDTO.getEndTime());
          invoicingState =
              CollUtil.defaultIfEmpty(invoicingState, supplierOrderSchemeDTO.getInvoicingState());
          type = StrUtil.blankToDefault(type, supplierOrderSchemeDTO.getType());
          title = StrUtil.blankToDefault(title, supplierOrderSchemeDTO.getTitle());
          excludeInvoicingState =
              StrUtil.blankToDefault(
                  excludeInvoicingState, supplierOrderSchemeDTO.getExcludeInvoicingState());
        }
      }
    }
    Page<Object[]> orderInvoicePage =
        orderDao.getOrderInvoicePageNew(
            orderNo,
            invoiceApplicationNum,
            orderState,
            platform,
            price,
            customer,
            orderStartTime,
            beginTime,
            endTime,
            invoicingState,
            type,
            title,
            excludeInvoicingState,
            null,
            supplierId,
            orderInvoicePageQuery.getPageNo(),
            orderInvoicePageQuery.getPageSize());
    // 获取发票金额总计
    orderInvoicePage
        .getContent()
        .forEach(
            objects -> {
              OrderInvoice invoice =
                  orderInvoiceDao.findOrderInvoiceByInvoiceApplicationNumber((String) objects[12]);
              List<OrderInvoiceToOrder> inToOrders =
                  orderInvoiceToOrderDao.getByInvoiceId(invoice.getId());
              BigDecimal priceSum = new BigDecimal(BigInteger.ZERO);
              for (OrderInvoiceToOrder inToOrder : inToOrders) {
                Order order = orderDao.get(inToOrder.getOrderId());
                priceSum = priceSum.add(order.getPrice());
              }
              objects[3] = priceSum;
            });
    PageResult<T> tPageResult =
        PageResultBuilder.buildPageResult(
            orderInvoicePage,
            (objects -> {
              T dto = ReflectUtil.newInstance(cls);
              dto.setId(String.valueOf(ObjectUtil.defaultIfNull(objects[0], "")));
              dto.setOrderNo(String.valueOf(ObjectUtil.defaultIfNull(objects[1], "")));
              dto.setPlatform(String.valueOf(ObjectUtil.defaultIfNull(objects[2], "")));
              dto.setPrice(
                  new BigDecimal(
                      String.valueOf(ObjectUtil.defaultIfNull(objects[3], BigDecimal.ZERO))));
              if (dto instanceof InvoiceDTO) {
                ((InvoiceDTO) dto)
                    .setType(String.valueOf(ObjectUtil.defaultIfNull(objects[4], "")));
                ((InvoiceDTO) dto)
                    .setTitle(String.valueOf(ObjectUtil.defaultIfNull(objects[5], "")));
              }
              dto.setEnterPriseName(String.valueOf(ObjectUtil.defaultIfNull(objects[6], "")));
              dto.setApplicationTime(String.valueOf(ObjectUtil.defaultIfNull(objects[7], "-")));
              dto.setInvoicingState(String.valueOf(ObjectUtil.defaultIfNull(objects[8], "")));
              dto.setCustomer(String.valueOf(ObjectUtil.defaultIfNull(objects[10], "")));
              dto.setCreateTime(String.valueOf(ObjectUtil.defaultIfNull(objects[11], "-")));
              dto.setInvoiceApplicationNumber(
                  String.valueOf(ObjectUtil.defaultIfNull(objects[12], "-")));
              return dto;
            }));
    return tPageResult;
  }

  @Override
  public void orderDelivery(DeliveryParamDTO deliveryParamDTO) {
    if (StringUtils.isNullOrEmpty(deliveryParamDTO.getOrderId())) {
      throw new CheckException("订单为空");
    }
    if (StringUtils.isNullOrEmpty(deliveryParamDTO.getSupplierId())) {
      throw new CheckException("supplierId 参数未传");
    }
    long currentTimeMillis = System.currentTimeMillis();
    RLock lock = null;
    TransactionStatus transactionStatus = null;
    Order order = null;
    OrderDelivery orderDelivery;
    List<RowAndQty> rowAndQtyList;
    Map<String, BigDecimal> codeAndNum;
    try {
      lock =
          redissonClient.getLock(
              Constants_LockName.ORDER_DELIVERY + deliveryParamDTO.getSupplierId());
      lock.lock();
      DefaultTransactionDefinition defaultTransactionDefinition =
          new DefaultTransactionDefinition();
      transactionStatus = platformTransactionManager.getTransaction(defaultTransactionDefinition);
      order =
          get(
              deliveryParamDTO.getOrderId(),
              () -> CheckException.noFindException(Order.class, deliveryParamDTO.getOrderId()));
      Object[] sums = orderDetailDao.getReturnCountAndShipCountAndSkuNumCount(order.getId());
      BigDecimal orderTotalShipNum = (BigDecimal) sums[1];
      //订单商品总数
      BigDecimal totalNum = (BigDecimal) sums[2];
      // 取消数量
      BigDecimal totalCancelNum = (BigDecimal) sums[3];
      if (Constants_order.ORDER_STATE_WITHDRAW.equals(order.getOrderState())) {
        throw new CheckException("该订单已撤回");
      }
      if (totalNum.compareTo(BigDecimal.ZERO) > 0) {
        BigDecimal totalUnshipNum = NumberUtil.sub(totalNum, orderTotalShipNum, totalCancelNum);
        if (totalUnshipNum.compareTo(BigDecimal.ZERO) <= 0) {
          throw new CheckException("该订单已全部发货");
        }
      }
      log.info("订单发货，order：" + JSON.toJSONString(order));
      // 记录第一次发货时间，且订单状态变为履约中
      if (order.getFirstShipTime() == null) {
        orderDao.optimisticLockUpdateOrder(order,
            o -> {
              o.setFirstShipTime(currentTimeMillis);
              o.setOrderState(Constants_order.ORDER_STATE_PROCESS);});
        // 如果为非背靠背则直接生成需付款
        if (!Boolean.TRUE.equals(order.getBackToBack())) {
          orderNeedPaymentService.save(null, order);
        }
      }
      repository.save(order);
      // 咸亨自有物流
      String express_code = "XHZYWL";
      if (!express_code.equals(deliveryParamDTO.getExpressCode())
          && StringUtils.isNullOrEmpty(deliveryParamDTO.getExpressNo())) {
        throw new CheckException("请填入物流单号");
      }
      String startDate = DateUtils.formatTimeStampToNormalDate(System.currentTimeMillis());
      String dateStr =
          DateUtils.formatTimeStampToNormalDate(System.currentTimeMillis()).replace("-", "");
      orderDelivery = new OrderDelivery();
      long count =
          orderDeliveryDao.getOrderDeliveryCountByOrderId(
                  startDate,
                  DateUtils.formatTimeStampToNormalDate(
                      DateUtils.parseNormalDateToTimeStamp(startDate) + 24 * 60 * 60 * 1000l))
              + 1;
      String number = StrUtil.padPre(String.valueOf(count), 8, '0');
      String deliveryNo = "FHD" + dateStr + number;
      String expressNo =
          !StringUtils.isNullOrEmpty(deliveryParamDTO.getExpressNo())
              ? deliveryParamDTO.getExpressNo()
              : System.currentTimeMillis() + "";
      orderDelivery.setDeliveryNo(deliveryNo);
      orderDelivery.setDeliveryState(Constants_order.DELIVERY_STATE_WAIT);
      orderDelivery.setOrder(order);
      orderDelivery.setOrderId(order.getId());
      orderDelivery.setExpressCode(StringUtils.emptyIfNull(deliveryParamDTO.getExpressCode()));
      orderDelivery.setExpressCompany(
          StringUtils.emptyIfNull(deliveryParamDTO.getExpressCompany()));
      orderDelivery.setExpressNo(expressNo);
      orderDelivery.setNumber(deliveryParamDTO.getNumber());
      orderDelivery.setState(Constants.STATE_OK);
      orderDelivery.setCreateTime(System.currentTimeMillis());
      orderDeliveryService.save(orderDelivery);
      // 新增附件
      for (FileDTO fileDTO : CollUtil.emptyIfNull(deliveryParamDTO.getFileList())) {
        if (StrUtil.isBlank(fileDTO.getId())) {
          continue;
        }
        fileService.saveFile(fileDTO, null, orderDelivery.getId(), Constants.FILE_TYPE_DELIVERY);
      }
      rowAndQtyList = new ArrayList<>();
      codeAndNum = new HashMap<>();
      if (CollUtil.isNotEmpty(deliveryParamDTO.getProductDetailList())) {
        JSONArray productList = new JSONArray();
        for (DeliveryProductParamDTO deliveryProductParamDTO :
            deliveryParamDTO.getProductDetailList()) {
          OrderDetail orderDetail =
              orderDetailDao.getOrderDetailByOrderIdAndCode(
                  order.getId(), deliveryProductParamDTO.getCode());
          if (orderDetail != null) {
            BigDecimal unshipNum = NumberUtil.sub(orderDetail.getNum(), orderDetail.getShipNum(),
             orderDetail.getCancelNum());
            if (deliveryProductParamDTO.getDelCount().compareTo(unshipNum) > 0) {
              throw new CheckException("[" + orderDetail.getCode() + "]商品发货数量大于未发数量");
            }
            OrderDeliveryDetail orderDeliveryDetail = new OrderDeliveryDetail();
            orderDeliveryDetail.setReturnNum(BigDecimal.ZERO);
            orderDeliveryDetail.setDelivery(orderDelivery);
            orderDeliveryDetail.setDeliveryId(orderDelivery.getId());
            orderDeliveryDetail.setShipNum(deliveryProductParamDTO.getDelCount());
            orderDeliveryDetail.setBrand(
                StringUtils.emptyIfNull(deliveryProductParamDTO.getBrand()));
            orderDeliveryDetail.setCode(StringUtils.emptyIfNull(deliveryProductParamDTO.getCode()));
            orderDeliveryDetail.setModel(
                StringUtils.emptyIfNull(deliveryProductParamDTO.getModel()));
            orderDeliveryDetail.setName(StringUtils.emptyIfNull(deliveryProductParamDTO.getName()));
            orderDeliveryDetail.setUnit(StringUtils.emptyIfNull(deliveryProductParamDTO.getUnit()));
            orderDeliveryDetail.setNum(deliveryProductParamDTO.getNum());
            orderDeliveryDetail.setPrice(deliveryProductParamDTO.getPrice());
            orderDeliveryDetail.setCreateTime(currentTimeMillis);
            orderDeliveryDetail.setState(Constants.STATE_OK);
            orderDeliveryDetail.setErpRowId(orderDetail.getErpRowId());
            orderDeliveryDetailRepository.save(orderDeliveryDetail);
            if (StrUtil.isNotBlank(orderDetail.getErpRowId())) {
              RowAndQty rowAndQty = new RowAndQty();
              rowAndQty.setRowId(orderDetail.getErpRowId());
              rowAndQty.setQty(deliveryProductParamDTO.getDelCount());
              rowAndQtyList.add(rowAndQty);
            }
            codeAndNum.put(orderDetail.getCode(), deliveryProductParamDTO.getDelCount());
            BigDecimal totalShipNum =
                orderDetail.getShipNum().add(deliveryProductParamDTO.getDelCount());
            BigDecimal cancelNum = orderDetail.getCancelNum();
            BigDecimal num = orderDetail.getNum();
            orderDetail.setShipNum(totalShipNum);
            orderDetail.setUnshipNum(
                NumberUtil.sub(orderDetail.getNum(), totalShipNum, cancelNum));
            orderDetail.setDeliveryState(
                OrderDeliveryProgress.getOrderDetailDeliveryProgress(totalShipNum, num, cancelNum)
                    .getCode());
            orderDetailRepository.save(orderDetail);
            JSONObject jsonObject = new JSONObject();
            if (!Constants.YES.equals(order.getOld())) {
              jsonObject.put(
                  "platformProductCode",
                  StringUtils.emptyIfNull(deliveryProductParamDTO.getCode()));
            } else {
              jsonObject.put("code", StringUtils.emptyIfNull(deliveryProductParamDTO.getCode()));
            }
            jsonObject.put(
                "num",
                deliveryProductParamDTO.getDelCount() != null
                    ? deliveryProductParamDTO.getDelCount()
                    : 0);
            jsonObject.put(
                "xsPrice",
                deliveryProductParamDTO.getPrice() != null
                    ? deliveryProductParamDTO.getPrice()
                    : 0);
            jsonObject.put(
                "dbPrice",
                deliveryProductParamDTO.getPrice() != null
                    ? deliveryProductParamDTO.getPrice()
                    : 0);
            productList.add(jsonObject);
          }
        }
        JSONObject params = new JSONObject();
        params.put("dockingOrderNo", order.getOrderNo());
        String dockingOrderType = StrUtil.emptyToDefault(order.getSubType(), order.getType());
        params.put("dockingOrderType", dockingOrderType);
        params.put("expressNo", expressNo);
        params.put("openNo", deliveryNo);
        params.put("logisticsCode", StringUtils.emptyIfNull(deliveryParamDTO.getExpressCode()));
        params.put(
            "logisticsCompany", StringUtils.emptyIfNull(deliveryParamDTO.getExpressCompany()));
        params.put(
            "openTime", DateUtils.formatTimeStampToNormalDateTime(System.currentTimeMillis()));
        params.put("productList", productList);
        //          JSONObject result;
        //          if (Constants.YES.equals(order.getOld())) {
        //            result = httpUtil.omsOrderDelivery(params.toString());
        //            log.info("oms发货请求返回:" + result);
        //          } else {
        //            result = httpUtil.platformOrderDelivery(params.toString());
        //            log.info("履约平台[" + order.getOrderNo() + "]发货请求返回:" + result);
        //          }
        //          if (result == null || !result.containsKey("code") || "1".equals(
        //              result.getString("code"))) {
        //            String message =
        //                result != null && result.containsKey("message") ?
        // result.getString("message")
        //                    : "履约平台发货异常";
        //            throw new CheckException("发货失败:" + message);
        //          }
      }
      // 判断是否全部发货
      BigDecimal sumUnShipCount = orderDetailDao.getSumUnShipCount(order.getId());
      if (NumberUtil.equals(sumUnShipCount, BigDecimal.ZERO)) {
        orderDao.optimisticLockUpdateOrder(order,
            o -> {
              o.setAllShipTime(currentTimeMillis);
              o.setOrderState(Constants_order.ORDER_STATE_ACCEPTING);}
        );
      }
      platformTransactionManager.commit(transactionStatus);
    } catch (CheckException checkException) {
      if (Objects.nonNull(transactionStatus)) {
        platformTransactionManager.rollback(transactionStatus);
      }
      throw checkException;
    } catch (Exception e) {
      if (Objects.nonNull(transactionStatus)) {
        platformTransactionManager.rollback(transactionStatus);
      }
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (Objects.nonNull(lock)) {
        lock.unlock();
      }
    }
    // 每天下午 5.30 后不能自动入库
    //    DateTime currentTime = DateTime.of(currentTimeMillis);
    //    int hour = currentTime.hour(true);
    //    int minute = currentTime.minute();
    //    Pair<Integer, Integer> autoWarehousingBefore = Constants.AUTO_WAREHOUSING_BEFORE;
    //    if (hour< autoWarehousingBefore.getKey()
    // ||(hour==autoWarehousingBefore.getKey()&&minute<autoWarehousingBefore.getValue())) {
    //
    //    }
    try {
      String id = order.getId();
      Order orderTemp = orderDao.get(id);
      log.warn(
          System.currentTimeMillis()
              + "【"
              + orderTemp.getOrderNo()
              + "】订单发货此时订单状态："
              + orderTemp.getOrderState());
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
    }
    try {
      Thread.sleep(500);
    } catch (InterruptedException e) {
    }
    // 异步进行入库
    asyncAddErpOrder(
        order.getId(),
        orderDelivery.getId(),
        orderDelivery.getExpressNo(),
        orderDelivery.getExpressCompany(),
        rowAndQtyList,
        codeAndNum,
        orderDelivery);
  }

  public void asyncAddErpOrder(
      final String orderId,
      final String orderDeliveryId,
      final String expressNo,
      final String logisticsCompany,
      final List<RowAndQty> rowAndQtyList,
      Map<String, BigDecimal> codeAndNum,
      OrderDelivery orderDelivery) {
    asyncUtil.execute(
        orderId,
        (id) -> {
          autoWarehousing(
              orderDeliveryId,
              expressNo,
              logisticsCompany,
              rowAndQtyList,
              codeAndNum,
              id,
              false,
              orderDelivery);
        });
  }

  public void updateErpOrderRate(UpdateErpOrderRateParams params) {
    erpRequest.updateErpOrderRate(params);
  }

  @Override
  public void autoWarehousing() {
    DateTime yesterdayBeginDay =
        DateUtil.beginOfDay(DateUtil.offsetDay(DateTime.of(System.currentTimeMillis()), -1));
    Pair<Integer, Integer> autoWarehousingBefore = Constants.AUTO_WAREHOUSING_BEFORE;
    Integer key = autoWarehousingBefore.getKey();
    Integer value = autoWarehousingBefore.getValue();
    // 昨日的下午 5.30
    DateTime startTime = DateUtil.offsetMinute(DateUtil.offsetHour(yesterdayBeginDay, key), value);
    DateTime endTime = DateUtil.endOfDay(yesterdayBeginDay);
    log.info("开始处理【" + startTime + "-" + endTime + "】时间段内产生的发货单");
    List<OrderDelivery> orderDeliveryList =
        CollUtil.emptyIfNull(
            orderDeliveryRepository.findAllByCreateTimeBetweenAndState(
                startTime.getTime(), endTime.getTime(), Constants.STATE_OK));
    for (OrderDelivery orderDelivery : orderDeliveryList) {
      String erpNo = orderDelivery.getErpNo();
      if (StrUtil.isNotBlank(erpNo)) {
        log.info("【" + orderDelivery.getDeliveryNo() + "】无需自动入库");
      } else {
        String orderDeliveryId = orderDelivery.getId();
        Order order = orderDelivery.getOrder();
        String expressNo = orderDelivery.getExpressNo();
        String logisticsCompany = orderDelivery.getExpressCompany();
        List<OrderDeliveryDetail> orderDeliveryDetailList =
            orderDeliveryDetailDao.getOrderDeliveryDetailByDeliveryId(orderDeliveryId);
        if (CollUtil.isNotEmpty(orderDeliveryDetailList)) {
          List<RowAndQty> rowAndQtyList = new ArrayList<>();
          Map<String, BigDecimal> codeAndNum = new HashMap<>(16);
          String id = order.getId();
          for (OrderDeliveryDetail orderDeliveryDetail : orderDeliveryDetailList) {
            OrderDetail orderDetail =
                orderDetailDao.getOrderDetailByOrderIdAndCode(id, orderDeliveryDetail.getCode());
            if (StrUtil.isNotBlank(orderDetail.getErpRowId())) {
              RowAndQty rowAndQty = new RowAndQty();
              rowAndQty.setRowId(orderDetail.getErpRowId());
              rowAndQty.setQty(orderDeliveryDetail.getShipNum());
              rowAndQtyList.add(rowAndQty);
            }
            codeAndNum.put(orderDetail.getCode(), orderDeliveryDetail.getShipNum());
          }
          log.info("【" + orderDelivery.getDeliveryNo() + "】开始自动入库");
          autoWarehousing(
              orderDeliveryId,
              expressNo,
              logisticsCompany,
              rowAndQtyList,
              codeAndNum,
              id,
              true,
              orderDelivery);
          log.info("【" + orderDelivery.getDeliveryNo() + "】结束自动入库");
        }
      }
    }
  }

  public String generatePurchaseOrderCode() {
    Random random = new Random();
    StringBuilder sb = new StringBuilder("20"); // 前两位固定为'20'
    // 生成后八位随机数字
    for (int i = 0; i < 8; i++) {
      int digit = random.nextInt(10);
      sb.append(digit);
    }
    return sb.toString();
  }

  private String addSapPurchaseOrder(Order order) {
    if (!OrderErpTypeEnum.SAP_ERP.getDescription().equals(order.getErpType())) return StrUtil.EMPTY;
    UpdatePurchaseOrderSapParam param = new UpdatePurchaseOrderSapParam();
    UpdatePurchaseOrderDATADTO data = new UpdatePurchaseOrderDATADTO();
    UpdatePurchaseOrderHEADDTO head = new UpdatePurchaseOrderHEADDTO();
    // 采购订单编号需要查库验重
    String purchaseOrderCode = generatePurchaseOrderCode();
    head.setSRMID(StrUtil.EMPTY);
    head.setSpras(StrUtil.EMPTY);
    head.setName1(StrUtil.EMPTY);
    head.setCity1(StrUtil.EMPTY);
    head.setWkurs(StrUtil.EMPTY);
    head.setEbeln(purchaseOrderCode);
    head.setBsart(PurchaseApplicationTypeEnum.STANDARD.getCode());
    head.setEkorg(order.getTitleOfTheContract());
    //如果签约抬头是总部传递001
    String ekgrp = "001";
    if (TitleOfTheContractEnum.TITLE_OF_WAN_JU.getCode().equals(head.getEkorg())) {
      //如果签约抬头是万聚传递251
      ekgrp = "251";
    }
    head.setEkgrp(ekgrp);
    head.setBukrs(order.getTitleOfTheContract());
    head.setZttwb(order.getOrderRemark());
    head.setBedat(DateUtil.format(DateUtil.date(), "yyyyMMdd"));
    Supplier supplier = order.getSupplier();
    String mdmCode = supplier.getMdmCode();
    head.setLifnr(mdmCode);
    head.setLifn2(mdmCode);
    head.setCountry("CN");
    head.setZterm("0001");
    head.setWaers("CNY");
    head.setZsp("X");
    SupplierPerformance supplierPerformance =
        supplierPerformanceService.getFirstBySupplierIdAndPlatformCode(
            order.getSupplierId(), order.getType());
    String dockingPurchaseErpCode = supplierPerformance.getDockingPurchaseErpCode();
    User user = userService.getByCode(dockingPurchaseErpCode);
    head.setBednr("XHGJ00" + user.getCode());
    head.setAfnam(user.getRealName());
    List<OrderDetail> orderDetails =
        orderDetailRepository.findAllByOrderIdAndState(order.getId(), Constants.STATE_OK);
    ArrayList<ITEMDTO> items = new ArrayList<>();
    for (OrderDetail orderDetail : orderDetails) {
      ITEMDTO item = new ITEMDTO();
      item.setEbelp(orderDetail.getPurchaseOrderRowNo());
      String productUnitCode = mpmServiceExt.getProductUnitCode(orderDetail.getInnerCode());
      item.setMatnr(orderDetail.getInnerCode());
      item.setMeins(productUnitCode);
      item.setMenge(String.valueOf(orderDetail.getNum()));
      item.setWerks(order.getTitleOfTheContract());
      item.setLgort("6000");
      item.setAplfz(DateUtil.today());
      String rateCode =
          Constants.TAX_RATE_TYPE_NUM.get(new BigDecimal(orderDetail.getCostPriceTaxRate()));
      item.setMwskz(rateCode);
      BigDecimal price = orderDetail.getPrice();
      Pair<BigDecimal, Integer> convertSapPrice = SAPToolUtils.convertSapPrice(price, 2);
      item.setNetpr(convertSapPrice.getKey().toPlainString());
      item.setPeinh(convertSapPrice.getValue().toString());
      item.setBprme(productUnitCode);
      BigDecimal mul =
          NumberUtil.mul(
                  NumberUtil.div(
                      orderDetail.getPrice(),
                      NumberUtil.add(
                          BigDecimal.ONE,
                          new BigDecimal(
                              Optional.ofNullable(orderDetail.getCostPriceTaxRate()).orElse("0")))),
                  NumberUtil.add(
                      BigDecimal.ONE,
                      new BigDecimal(
                          Optional.ofNullable(orderDetail.getSalesPriceTaxRate()).orElse("0"))))
              .multiply(new BigDecimal("1.02"));
      BigDecimal zjsj = NumberUtil.round(mul, 2, RoundingMode.UP);
      item.setZjsj(zjsj.stripTrailingZeros().toPlainString());
      item.setCharX(purchaseOrderCode);
      item.setPstyp("");
      item.setZZVBELN(order.getSaleOrderNo());
      item.setZZPOSNR(orderDetail.getRowNo());
      item.setElikz(StrUtil.EMPTY);
      item.setKnttp(StrUtil.EMPTY);
      item.setLoekz(StrUtil.EMPTY);
      item.setMatkl(StrUtil.EMPTY);
      item.setRetpo(StrUtil.EMPTY);
      item.setBanfn(StrUtil.EMPTY);
      item.setBnfpo(StrUtil.EMPTY);
      item.setKostl(StrUtil.EMPTY);
      item.setZgsje(StrUtil.EMPTY);
      item.setZmfbs(StrUtil.EMPTY);
      item.setZyyje(StrUtil.EMPTY);
      items.add(item);
    }
    head.setItem(items);
    data.setHead(head);
    param.setData(data);
    UpdatePurchaseOrderRETURNDTO updated = sapService.sapPurchaseOrderWithAlarm(param, order.getOrderNo());
    orderDao.optimisticLockUpdateOrder(order, o -> o.setErpOrderNo(updated.getEbeln()));
    return updated.getEbeln();
  }

  private void autoWarehousing(
      String orderDeliveryId,
      String expressNo,
      String logisticsCompany,
      List<RowAndQty> rowAndQtyList,
      Map<String, BigDecimal> codeAndNum,
      String id,
      boolean isSyncExecute,
      OrderDelivery orderDelivery) {
    try {
      try {
        Thread.sleep(5000);
      } catch (InterruptedException e) {
      }
      Order order = get(id);
      if (order == null) {
        log.warn("自动入库流程，订单为空。id:" + id);
        return;
      }
      log.info("【" + order.getOrderNo() + "】订单状态：" + order.getOrderState());
      // 调用ERP入库接口
      String erpOrderId = order.getErpOrderId();
      String erpOrderCode = order.getErpOrderNo();
      List<RowAndQty> finalRowAndQty = new ArrayList<>();
      if (StrUtil.isBlank(erpOrderCode)) {
        if (OrderErpTypeEnum.SAP_ERP.getDescription().equals(order.getErpType())) {
          addSapPurchaseOrder(order);
          //处理入库逻辑
          sapDeliverWarehousing(orderDelivery, order, isSyncExecute);
          return;
        } else {
          String saleOrderNo = order.getSaleOrderNo();
          if (StrUtil.isBlank(saleOrderNo)) {
            throw new CheckException("当前订单无销售订单号，无法新增向 ERP 新增采购单");
          }
          String erpOrderNo = order.getErpOrderNo();
          if (StrUtil.isNotBlank(erpOrderNo)) {
            throw new CheckException("已经存在采购单号，无法进行下推");
          }
          Supplier supplier = order.getSupplier();
          String mdmCode =
              Optional.ofNullable(supplier).map(Supplier::getMdmCode).orElse(StrUtil.EMPTY);
          if (StrUtil.isBlank(mdmCode)) {
            throw new CheckException("当前订单无法获取到 mdm 编码，无法新增向 ERP 新增采购单");
          }
          String type = order.getType();
          String purchaseErpCode;
          if (StrUtil.isNotBlank(type)) {
            purchaseErpCode =
                Optional.ofNullable(supplier)
                    .map(Supplier::getId)
                    .map(
                        supplierId ->
                            supplierPerformanceService.getFirstBySupplierIdAndPlatformCode(
                                supplierId, type))
                    .map(SupplierPerformance::getDockingPurchaseErpCode)
                    .orElse(StrUtil.EMPTY);
          } else {
            purchaseErpCode = StrUtil.EMPTY;
          }
          Pair<Pair<String, String>, Map<String, String>> orderIdCodeAndProductIdAndCodeMap =
              erpRequest.addPoOrder(
                  order.getOrderNo(),
                  saleOrderNo,
                  mdmCode,
                  purchaseErpCode,
                  order.getOrderRemark());
          if (orderIdCodeAndProductIdAndCodeMap == null) {
            throw new CheckException("调用 ERP 新增采购单失败");
          }
          Pair<String, String> erpOrderIdAndErpCode = orderIdCodeAndProductIdAndCodeMap.getKey();
          erpOrderId = erpOrderIdAndErpCode.getKey();
          erpOrderCode = erpOrderIdAndErpCode.getValue();
          Order orderCur = get(id, () -> CheckException.noFindException(Order.class, id));
          orderCur.setErpOrderId(erpOrderId);
          orderCur.setErpOrderNo(erpOrderCode);
          save(orderCur);
          Map<String, String> productCodeAndErpRowId = orderIdCodeAndProductIdAndCodeMap.getValue();
          List<OrderDetail> orderDetailList = orderDetailDao.getOrderDetailByOrderId(order.getId());
          ArrayList<Entry> entries = new ArrayList<>();
          for (OrderDetail orderDetail : orderDetailList) {
            String code = orderDetail.getInnerCode();
            String erpRowId = productCodeAndErpRowId.get(code);
            if (StrUtil.isNotBlank(erpRowId)) {
              orderDetail.setErpRowId(erpRowId);
              orderDetailService.save(orderDetail);
              BigDecimal val = codeAndNum.get(orderDetail.getCode());
              if (val != null) {
                RowAndQty rowAndQty = new RowAndQty();
                rowAndQty.setRowId(erpRowId);
                rowAndQty.setQty(val);
                finalRowAndQty.add(rowAndQty);
              }
              // 修改税率接口param
              Entry entry = new Entry();
              if (NumberUtil.isNumber(orderDetail.getCostPriceTaxRate())) {
                BigDecimal decimal = new BigDecimal(orderDetail.getCostPriceTaxRate());
                decimal = decimal.multiply(new BigDecimal(100));
                entry.setTaxRate(decimal);
              }
              entry.setTaxPrice(orderDetail.getPrice());
              entry.setEntryId(
                  StrUtil.isBlank(orderDetail.getErpRowId())
                      ? null
                      : Integer.parseInt(orderDetail.getErpRowId()));
              entries.add(entry);
            }
          }
          boolean updateErpOrderRate = isUpdateErpOrderRate(orderDetailList, order);
          if (updateErpOrderRate) {
            UpdateErpOrderRateParams params = new UpdateErpOrderRateParams();
            OrderData orderData = new OrderData();
            orderData.setOrderNo(erpOrderCode);
            orderData.setEntries(entries);
            params.setOrderData(orderData);
            updateErpOrderRate(params);
          }
        }
      } else {
        // 处理入库逻辑
        sapDeliverWarehousing(orderDelivery, order, isSyncExecute);
        finalRowAndQty.addAll(rowAndQtyList);
      }
      try {
        if (StrUtil.isNotBlank(erpOrderId)
            && StrUtil.isNotBlank(erpOrderCode)
            && CollUtil.isNotEmpty(finalRowAndQty)) {
          OrderDelivery dbOrderDelivery = orderDeliveryService.get(orderDeliveryId);
          if (dbOrderDelivery != null) {
            Pair<String, String> addBillNoAndMsg =
                erpRequest.addBill(
                    erpOrderId,
                    erpOrderCode,
                    StrUtil.emptyIfNull(expressNo),
                    finalRowAndQty,
                    StrUtil.emptyIfNull(logisticsCompany),
                    false,
                    order.getOrderNo(),
                    StrUtil.EMPTY,
                    isSyncExecute);
            dbOrderDelivery.setErpNo(addBillNoAndMsg.getKey());
            dbOrderDelivery.setOrderErpNo(order.getErpOrderNo());
            orderDeliveryService.save(dbOrderDelivery);
          }
        }
      } catch (Exception e) {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
      }
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
    }
  }

  private void sapDeliverWarehousing(OrderDelivery orderDelivery, Order order, boolean isSyncExecute) {
    if (!Objects.equals(order.getErpType(), OrderErpTypeEnum.SAP_ERP.getDescription())) {
      throw new CheckException("订单所属erp类型不符");
    }
    List<OrderDeliveryDetail> orderDeliveryDetails =
        orderDeliveryDetailDao.getOrderDeliveryDetailByDeliveryId(orderDelivery.getId());
    Map<OrderDeliveryDetail, OrderDetail> orderDeliveryDetailOrderDetailMap =
        CollUtil.emptyIfNull(orderDeliveryDetails).stream()
            .collect(
                Collectors.toMap(
                    Function.identity(),
                    orderDeliveryDetail -> {
                      OrderDetail orderDetail =
                          orderDetailDao.getOrderDetailByOrderIdAndCode(
                              order.getId(), orderDeliveryDetail.getCode());
                      if (orderDetail == null) {
                        throw new CheckException("数据异常，请联系管理员！");
                      }
                      OrderDetail currenOrderDetail = MapStructFactory.INSTANCE.toOrderDetail(orderDetail);
                      String productUnitCode = mpmServiceExt.getProductUnitCode(orderDetail.getInnerCode());
                      currenOrderDetail.setUnit(productUnitCode);
                      return currenOrderDetail;
                    },
                    (existing, replacement) -> existing,    // 合并函数
                    LinkedHashMap::new  // 使用 LinkedHashMap 保持顺序
                    ));
    ReceiptVoucherSynchronizationParam param =
        ReceiptVoucherSynchronizationParam.of(
            order, orderDelivery, orderDeliveryDetailOrderDetailMap);
    ReceiptVoucherSynchronizationResult result = null;
    try {
      // 同步操作则使用锁
      if (isSyncExecute) {
        result = sapService.sapMaterialVoucherWithLockGroup(param);
      } else {
        // 异步不使用锁
        result = sapService.sapMaterialVoucherWithError(param, new ReceiptVoucherSynchronizationResult());
      }
    }catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException("SAP系统响应异常，请联系管理员！");
    }
    List<ReturnMessage> returnMessages = result.getReturnMessages();
    // 物料凭证号是一致的
    orderDelivery.setErpNo(returnMessages.get(0).getDocumentNumber());
    List<Item> items = param.getData().getHead().getItems();
    orderDelivery.setOrderErpNo(items.get(0).getPurchaseOrderNumber());
    orderDeliveryService.save(orderDelivery);
    for (int i = 0; i < returnMessages.size(); i++) {
      OrderDeliveryDetail orderDeliveryDetail = orderDeliveryDetails.get(i);
      ReturnMessage returnMessage = returnMessages.get(i);
      String lineItem = returnMessage.getLineItem();
      String batchNo = returnMessage.getCharge();
      orderDeliveryDetail.setErpRowId(lineItem);
      orderDeliveryDetail.setBatchNo(batchNo);
      orderDeliveryDetailRepository.save(orderDeliveryDetail);
    }
  }

  /** 是否调用修改税率接口 */
  private boolean isUpdateErpOrderRate(List<OrderDetail> orderDetailList, Order order) {
    // 是否调用修改税率接口
    AtomicBoolean sign = new AtomicBoolean(true);
    if (StrUtil.isBlank(order.getSupplierId())) {
      sign.set(false);
    }
    // 物料不同的税率
    Set<BigDecimal> set = getProductRate(orderDetailList);
    if (sign.get()) {
      String supplierId = order.getSupplierId();
      // 供应商税率
      Optional<SupplierInGroup> supplierIdAndGroupIdAndState =
          supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(
              supplierId, Constants.GROUP_WANJU_ID, Constants.STATE_OK);
      supplierIdAndGroupIdAndState.ifPresent(
          supplierInGroup -> {
            String taxRateStr = SupplierConstants.TAX_RATE_MAP.get(supplierInGroup.getTaxRate());
            if (!NumberUtil.isNumber(taxRateStr)) {
              log.error("供应商默认税率脏数据，供应商id：" + supplierId);
              throw new CheckException("供应商默认税率脏数据！");
            }
            BigDecimal bigDecimal = new BigDecimal(taxRateStr);
            for (BigDecimal decimal : set) {
              if (NumberUtil.equals(bigDecimal, decimal)) {
                sign.set(false);
              }
            }
          });
    }
    return sign.get();
  }

  // 物料不同的税率
  private Set<BigDecimal> getProductRate(List<OrderDetail> orderDetailList) {
    return CollUtil.emptyIfNull(orderDetailList).stream()
        .map(
            orderDetail -> {
              String taxRate = orderDetail.getCostPriceTaxRate();
              if (NumberUtil.isNumber(taxRate)) {
                return NumberUtil.mul(taxRate, "100");
              }
              return null;
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  @Override
  public void orderDeliveryUpdate(DeliveryParamDTO deliveryParamDTO) {
    if (StringUtils.isNullOrEmpty(deliveryParamDTO.getDeliveryId())) {
      throw new CheckException("发货单为空");
    }
    OrderDelivery orderDelivery = orderDeliveryDao.get(deliveryParamDTO.getDeliveryId());
    if (orderDelivery != null) {
      orderDelivery.setExpressCode(StringUtils.emptyIfNull(deliveryParamDTO.getExpressCode()));
      orderDelivery.setExpressCompany(
          StringUtils.emptyIfNull(deliveryParamDTO.getExpressCompany()));
      orderDelivery.setExpressNo(StringUtils.emptyIfNull(deliveryParamDTO.getExpressNo()));
      orderDeliveryRepository.save(orderDelivery);
    } else {
      throw new CheckException("发货单为空");
    }
  }

  @Override
  public DeliveryOrderDetailDTO getOrderDeliveryDetail(String orderDeliveryId) {
    if (StringUtils.isNullOrEmpty(orderDeliveryId)) {
      throw new CheckException("发货单为空");
    }
    OrderDelivery orderDelivery = orderDeliveryDao.get(orderDeliveryId);
    if (orderDelivery != null) {
      List<FileDTO> fileDTOS = CollUtil.emptyIfNull(
              fileService.getFileListByIdAndType(orderDelivery.getId(), Constants.FILE_TYPE_DELIVERY))
          .stream().map(item -> new FileDTO(item, url)).collect(Collectors.toList());
      DeliveryOrderDetailDTO deliveryOrderDetailDTO = new DeliveryOrderDetailDTO(orderDelivery);
      deliveryOrderDetailDTO.setFileList(fileDTOS);
      List<OrderDeliveryDetailDTO> orderDeliveryDetailDTOS = new ArrayList<>();
      List<OrderDeliveryDetail> orderDeliveryDetails =
          orderDeliveryDetailDao.getOrderDeliveryDetailByDeliveryId(orderDelivery.getId());
      if (orderDeliveryDetails != null && orderDeliveryDetails.size() > 0) {
        for (OrderDeliveryDetail orderDeliveryDetail : orderDeliveryDetails) {
          OrderDeliveryDetailDTO orderDeliveryDetailDTO =
              new OrderDeliveryDetailDTO(orderDeliveryDetail);
          orderDeliveryDetailDTOS.add(orderDeliveryDetailDTO);
        }
        deliveryOrderDetailDTO.setDeliveryProductList(orderDeliveryDetailDTOS);
      }
      return deliveryOrderDetailDTO;
    } else {
      throw new CheckException("发货单为空");
    }
  }

  @Override
  public List<ExpressCompanyDTO> getExpressCompanyList(String type) {
    JSONObject res;
    if (Constants_order.ORDER_PLATFORM_ZCY.equals(type)) {
      res = httpUtil.getDockingExpressCompany(type);
    } else {
      res = httpUtil.getExpressCompany("");
    }
    List<ExpressCompanyDTO> expressCompanyDTOS = new ArrayList<>();
    if (res != null && res.getInteger("code") == 0) {
      JSONArray data = res.containsKey("data") ? res.getJSONArray("data") : null;
      if (data != null && data.size() > 0) {
        for (int i = 0; i < data.size(); i++) {
          JSONObject company = data.getJSONObject(i);
          ExpressCompanyDTO expressCompanyDTO = new ExpressCompanyDTO();
          expressCompanyDTO.setExpressCompany(
              company.containsKey("name") ? company.getString("name") : "");
          expressCompanyDTO.setExpressCode(
              company.containsKey("code") ? company.getString("code") : "");
          expressCompanyDTOS.add(expressCompanyDTO);
        }
        /*ExpressCompanyDTO expressCompanyDTO = new ExpressCompanyDTO();
        expressCompanyDTO.setExpressCompany("商家配送");
        expressCompanyDTO.setExpressCode("XHZYWL");
        expressCompanyDTOS.add(expressCompanyDTO);*/
      }
    }
    return expressCompanyDTOS;
  }


  @SneakyThrows
  @Override
  public FileByteAndType downloadAcceptTemp(String id) {
    if (StringUtils.isNullOrEmpty(id)) {
      throw new CheckException("操作失败,订单id为空!");
    }
    Order order =
        repository.findById(id).orElseThrow(() -> CheckException.noFindException(Order.class, id));
    // 如果订单的平台是华能商城
    if (Constants_order.ORDER_PLATFORM_HNSC.equals(order.getTypeName())
        || Constants_order.ORDER_PLATFORM_HNSC_CODE.equals(order.getType())
        || Constants_order.ORDER_PLATFORM_GTSC_CODE.equals(order.getSubType())
    ) {
      return dockService.getDownloadDeliveryDocumentForByte(
          new DownloadDeliveryDocumentForm(order.getType(), order.getOrderNo()));
    }
    // 采购单位名称
    String customer = StrUtil.emptyIfNull(order.getCustomer());
    // 客户单号
    String orderNo = StrUtil.emptyIfNull(order.getOrderNo());
    // 收货人
    String receiveMan = StrUtil.emptyIfNull(order.getConsignee());
    // 联系电话
    String receiveMobile = StrUtil.emptyIfNull(order.getMobile());
    // 收货地址
    String receiveAddress = StrUtil.emptyIfNull(order.getAddress());
    String contacts;
    String mobile;
    String type = order.getType();
    if (StrUtil.isNotBlank(type)) {
      PlatformMapContactMobileEnums platformMapContactMobileEnums =
          BootDictEnumUtil.getEnumByKey(PlatformMapContactMobileEnums.class, type)
              .orElse(PlatformMapContactMobileEnums.OTHER);
      contacts = platformMapContactMobileEnums.getContact();
      mobile = platformMapContactMobileEnums.getMobile();
    } else {
      contacts = StrUtil.EMPTY;
      mobile = StrUtil.EMPTY;
    }
    List<String> projectNo =
        getOrderLargeTicketProjectNo(order, order.getOrderNo(), order.getType());
    Map<String, String> fillInTemplateContent = new HashMap<>();
    fillInTemplateContent.put("${customer}", customer);
    fillInTemplateContent.put("${orderNo}", orderNo);
    fillInTemplateContent.put("${receiveMan}", receiveMan);
    fillInTemplateContent.put("${receiveMobile}", receiveMobile);
    fillInTemplateContent.put("${receiveAddress}", receiveAddress);
    fillInTemplateContent.put("${contacts}", contacts);
    fillInTemplateContent.put("${mobile}", mobile);
    // v5.8.1新增大票项目号字段
    if (CollUtil.isNotEmpty(projectNo)) {
      fillInTemplateContent.put("${projectNo}", StrUtil.join("、", projectNo));
    }
    try (InputStream inputStreamFromOSS = selectAcceptTemp(type)) {
      if (inputStreamFromOSS == null) {
        throw new CheckException("未找到送货单模板，请联系管理员放置模板");
      }
      try (XWPFDocument doc = new XWPFDocument(inputStreamFromOSS);) {
        // 替换模板中的占位符号
        WordUtil.replaceTable(doc, fillInTemplateContent, "accpetTemp");
        // 填写订单信息
        List<OrderDetail> orderDetails =
            CollUtil.emptyIfNull(orderDetailDao.getOrderDetailByOrderId(id));
        // 加上合计行
        int size = orderDetails.size() + 1;
        XWPFTable tableArray = doc.getTableArray(0);
        if (size > 2) {
          WordUtil.addRows(tableArray, 9, size - 2, 9);
        }
        int i = 9;
        setRowsText(type, tableArray, orderDetails, i);
        // 合计 订单数量、发货数量
        if (!StrUtil.equals(order.getType(), Constants_order.ORDER_PLATFORM_ZGDJ)) {
          // 非电建单子做优化
          BigDecimal numSum =
              orderDetails.stream().map(OrderDetail::getNum).reduce(BigDecimal.ZERO, BigDecimal::add);
          BigDecimal shipNumSum = orderDetails.stream().map(OrderDetail::getShipNum)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
          WordUtil.setRowsText(tableArray.getRow(i + orderDetails.size()), "合计", "", "", "", "", "",
              numSum.stripTrailingZeros().toPlainString(),
              shipNumSum.stripTrailingZeros().toPlainString());
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        doc.write(out);
        return new FileByteAndType(out.toByteArray());
      }
    }
  }

  private InputStream streamTran(ByteArrayOutputStream in) {
    return new ByteArrayInputStream(in.toByteArray());
  }

  private static void setRowsTextByDefault(
      XWPFTable tableArray, Collection<OrderDetail> orderDetails, int index) {
    for (OrderDetail orderDetail : orderDetails) {
      WordUtil.setRowsText(
          tableArray.getRow(index),
          String.valueOf(index - 8),
          StrUtil.emptyIfNull(orderDetail.getCode()),
          StrUtil.emptyIfNull(orderDetail.getBrand()),
          StrUtil.emptyIfNull(orderDetail.getName()),
          StrUtil.emptyIfNull(orderDetail.getModel()),
          StrUtil.emptyIfNull(orderDetail.getUnit()),
          CommonlyUseUtil.BigDecimalValue(
              String.valueOf(ObjectUtil.defaultIfNull(orderDetail.getNum(), 0L))),
          CommonlyUseUtil.BigDecimalValue(
              String.valueOf(ObjectUtil.defaultIfNull(orderDetail.getShipNum(), 0L))));
      index++;
    }
  }

  private static void setRowsTextByZgdj(
      XWPFTable tableArray, Collection<OrderDetail> orderDetails, int index) {
    for (OrderDetail orderDetail : orderDetails) {
      WordUtil.setRowsText(
          tableArray.getRow(index),
          String.valueOf(index - 8),
          StrUtil.emptyIfNull(orderDetail.getCode()),
          StrUtil.emptyIfNull(orderDetail.getBrand()),
          // 只展示物料名称
          StrUtil.emptyIfNull(orderDetail.getName()),
          StrUtil.emptyIfNull(orderDetail.getModel()),
          StrUtil.emptyIfNull(orderDetail.getUnit()),
          CommonlyUseUtil.BigDecimalValue(
              String.valueOf(ObjectUtil.defaultIfNull(orderDetail.getNum(), 0L))),
          CommonlyUseUtil.BigDecimalValue(
              String.valueOf(ObjectUtil.defaultIfNull(orderDetail.getShipNum(), 0L))),
          String.valueOf(
              ObjectUtil.defaultIfNull(
                  orderDetail.getSalePrice().setScale(2, RoundingMode.HALF_UP), 0L)));
      index++;
    }
  }

  private void setRowsText(
      String type, XWPFTable tableArray, Collection<OrderDetail> orderDetails, int index) {
    switch (type) {
      case Constants_order.ORDER_PLATFORM_ZGDJ:
        setRowsTextByZgdj(tableArray, orderDetails, index);
        break;
        // 能建暂不做特殊处理
        //      case Constants_order.ORDER_PLATFORM_NJSC:
        //        setRowsTextByNjsc(tableArray, orderDetails, index);
        //        break;
      default:
        setRowsTextByDefault(tableArray, orderDetails, index);
    }
  }

  /**
   * 选择验收单模板
   *
   * @param type 项目
   * @return 验收单inputStream
   */
  private InputStream selectAcceptTemp(String type) {
    try {
      if (Objects.equals(Constants_order.ORDER_PLATFORM_ZGDJ, type)) {
        return downloadThenUpUtil.getInputStreamFromOSS(DELIVERY_NOTE_URL_TYPE_ZGDJ);
      }
      if (Objects.equals(Constants_order.ORDER_PLATFORM_NJSC, type)) {
        return downloadThenUpUtil.getInputStreamFromOSS(DELIVERY_NOTE_URL_TYPE_NJSC);
      }
      return downloadThenUpUtil.getInputStreamFromOSS(DELIVERY_NOTE_URL);
    } catch (CheckException e) {
      throw new CheckException("未找到送货单模板，请联系管理员放置模板");
    }
  }

  @SneakyThrows
  @Override
  public byte[] exportOrderDetail(List<String> ids, String supplierId, String state) {
    if (!CollUtil.isNotEmpty(ids)) {
      List<String> orderPageStates = new ArrayList<>();
      if (Constants_order.ORDER_SEARCH_STATE_ALL.equals(state)) {
        orderPageStates.add(Constants_order.ORDER_STATE_ACCEPTED);
        orderPageStates.add(Constants_order.ORDER_STATE_RETURN);
        orderPageStates.add(Constants_order.ORDER_STATE_CANCEL);
        orderPageStates.add(Constants_order.ORDER_STATE_WITHDRAW);
      } else {
        if (!StringUtils.isNullOrEmpty(state)) {
          orderPageStates.add(state);
        }
      }
      ids = orderDao.getOrderListByOrderState(supplierId, orderPageStates);
    }

    // 使用SXSSFWorkbook替代XSSFWorkbook以减少内存占用
    try (SXSSFWorkbook workbook = new SXSSFWorkbook()) {
      // 设置内存中保留的行数，超过这个数量的行会被写入临时文件
      workbook.setCompressTempFiles(true);

      Sheet sheet = exportUtil.createSheet(
          workbook,
          "订单明细",
          Arrays.asList(
              30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30,
              30));
      CellStyle baseStyle = exportUtil.getBaseStyle(workbook);
      CellStyle titleStyle = exportUtil.getTitleStyle(workbook);
      Row title = sheet.createRow(0);
      exportUtil.createCell(title, 0, "客户订单号", titleStyle);
      exportUtil.createCell(title, 1, "下单时间", titleStyle);
      exportUtil.createCell(title, 2, "下单平台", titleStyle);
      exportUtil.createCell(title, 3, "下单金额", titleStyle);
      exportUtil.createCell(title, 4, "客户名称", titleStyle);
      exportUtil.createCell(title, 5, "收件人", titleStyle);
      exportUtil.createCell(title, 6, "联系方式", titleStyle);
      exportUtil.createCell(title, 7, "收货地址", titleStyle);
      exportUtil.createCell(title, 8, "物料序号", titleStyle);
      exportUtil.createCell(title, 9, "进度", titleStyle);
      exportUtil.createCell(title, 10, "物料编码", titleStyle);
      exportUtil.createCell(title, 11, "品牌", titleStyle);
      exportUtil.createCell(title, 12, "商品名称", titleStyle);
      exportUtil.createCell(title, 13, "型号", titleStyle);
      exportUtil.createCell(title, 14, "数量", titleStyle);
      exportUtil.createCell(title, 15, "单位", titleStyle);
      exportUtil.createCell(title, 16, "单价", titleStyle);
      exportUtil.createCell(title, 17, "未发数量", titleStyle);
      exportUtil.createCell(title, 18, "已发数量", titleStyle);
      exportUtil.createCell(title, 19, "退货数量", titleStyle);
      exportUtil.createCell(title, 20, "取消数量", titleStyle);

      int startRow = 1;
      // 分批处理订单，避免一次性加载过多数据到内存
      int batchSize = 100; // 每批处理100个订单
      for (int i = 0; i < ids.size(); i += batchSize) {
        List<String> batchIds = ids.subList(i, Math.min(i + batchSize, ids.size()));
        startRow = processBatchOrders(batchIds, sheet, baseStyle, startRow);
      }

      // 创建临时文件而不是直接返回字节数组
      File tempFile = File.createTempFile("order_detail_export_", ".xlsx");
      try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
        workbook.write(fileOutputStream);
        workbook.dispose(); // 清理临时文件
      }

      // 读取临时文件内容并返回字节数组
      byte[] result = Files.readAllBytes(tempFile.toPath());
      // 删除临时文件
      tempFile.delete();

      return result;
    }
  }

  /**
   * 分批处理订单数据，减少内存占用
   */
  private int processBatchOrders(List<String> batchIds, Sheet sheet, CellStyle baseStyle, int startRow) {
    for (String id : batchIds) {
      Order order = repository
          .findById(id)
          .orElseThrow(() -> CheckException.noFindException(Order.class, id));
      List<OrderDetail> orderDetails = orderDetailDao.getOrderDetailByOrderId(id);
      if (CollUtil.isNotEmpty(orderDetails)) {
        int detailRow = 1;
        for (OrderDetail orderDetail : orderDetails) {
          Row row = sheet.createRow(startRow);
          exportUtil.createCell(row, 0, StringUtils.emptyIfNull(order.getOrderNo()), baseStyle);
          exportUtil.createCell(
              row,
              1,
              order.getOrderTime() > 0
                  ? DateUtils.formatTimeStampToNormalDate(order.getOrderTime())
                  : "",
              baseStyle);
          exportUtil.createCell(
              row, 2, PlatformUtil.getPlatformNameByCode(order.getType()), baseStyle);
          exportUtil.createCell(row, 3, order.getPrice(), baseStyle);
          exportUtil.createCell(row, 4, StringUtils.emptyIfNull(order.getCustomer()), baseStyle);
          exportUtil.createCell(row, 5, StringUtils.emptyIfNull(order.getConsignee()), baseStyle);
          exportUtil.createCell(row, 6, StringUtils.emptyIfNull(order.getMobile()), baseStyle);
          exportUtil.createCell(row, 7, StringUtils.emptyIfNull(order.getAddress()), baseStyle);
          exportUtil.createCell(row, 8, detailRow, baseStyle);
          BigDecimal orderDetailCancelCount = orderDetail.getCancelNum();
          BigDecimal lastCount =
              NumberUtil.sub(
                  orderDetail.getNum(), orderDetailCancelCount, orderDetail.getShipNum());

          String progress =
              Constants_order.ORDER_DETAIL_SHIP_STATE_MAP.get(
                  Constants_order.ORDER_DETAIL_SHIP_STATE_UN);
          if (NumberUtil.equals(lastCount, BigDecimal.ZERO)) {
            progress =
                Constants_order.ORDER_DETAIL_SHIP_STATE_MAP.get(
                    Constants_order.ORDER_DETAIL_SHIP_STATE_ALL);
          } else if (lastCount.compareTo(BigDecimal.ZERO) > 0
              && (orderDetail.getNum().compareTo(lastCount) > 0)) {
            progress =
                Constants_order.ORDER_DETAIL_SHIP_STATE_MAP.get(
                    Constants_order.ORDER_DETAIL_SHIP_STATE_PART);
          }
          exportUtil.createCell(row, 9, progress, baseStyle);
          exportUtil.createCell(row, 10, StringUtils.emptyIfNull(orderDetail.getCode()), baseStyle);
          exportUtil.createCell(
              row, 11, StringUtils.emptyIfNull(orderDetail.getBrand()), baseStyle);
          exportUtil.createCell(row, 12, StringUtils.emptyIfNull(orderDetail.getName()), baseStyle);
          exportUtil.createCell(
              row, 13, StringUtils.emptyIfNull(orderDetail.getModel()), baseStyle);
          exportUtil.createCell(row, 14, orderDetail.getNum(), baseStyle);
          exportUtil.createCell(row, 15, StringUtils.emptyIfNull(orderDetail.getUnit()), baseStyle);
          exportUtil.createCell(row, 16, orderDetail.getPrice(), baseStyle);
          exportUtil.createCell(row, 17, orderDetail.getUnshipNum(), baseStyle);
          exportUtil.createCell(row, 18, orderDetail.getShipNum(), baseStyle);
          exportUtil.createCell(row, 19, orderDetail.getReturnNum(), baseStyle);
          exportUtil.createCell(row, 20, orderDetailCancelCount, baseStyle);
          detailRow += 1;
          startRow += 1;
        }
      }
    }
    return startRow;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void submitOrderInvoice(OrderInvoiceParams orderInvoiceParams) {
    Assert.notNull(orderInvoiceParams);
    String orderId = orderInvoiceParams.getOrderId();
    Order order = get(orderId, () -> CheckException.noFindException(Order.class, orderId));
    OrderInvoice orderInvoiceOld = orderInvoiceService.getByOrderId(order.getId());
    if (orderInvoiceOld != null) {
      throw new CheckException("发票申请已经提交，请联系管理员！");
    }
    OrderInvoice orderInvoice = new OrderInvoice();
    orderInvoice.setOrderId(order.getId());
    orderInvoice.setType(orderInvoiceParams.getOrderInvoiceEnums().getType());
    orderInvoice.setTitle(orderInvoiceParams.getTitle());
    orderInvoice.setTaxNumber(orderInvoiceParams.getTaxNumber());
    orderInvoice.setBankName(orderInvoiceParams.getBankName());
    orderInvoice.setBankAccount(orderInvoiceParams.getBankAccount());
    orderInvoice.setMobile(orderInvoiceParams.getMobile());
    orderInvoice.setAddress(orderInvoiceParams.getAddress());
    orderInvoice.setContent(orderInvoiceParams.getContent());
    orderInvoice.setReceiveMan(orderInvoiceParams.getReceiveMan());
    orderInvoice.setReceiveAddress(orderInvoiceParams.getReceiveAddress());
    orderInvoice.setRemark(orderInvoiceParams.getRemark());
    orderInvoice.setReceiveMobile(orderInvoiceParams.getReceiveMobile());
    orderInvoice.setCreateTime(System.currentTimeMillis());
    orderInvoice.setInvoiceApplicationNumber(
        OrderUtil.generateInvoiceApplicationNumber(
            Constants_order.ORDER_INVOICE_TYPE_SINGLE, order.getOrderNo()));
    orderInvoiceService.save(orderInvoice);
    order.setInvoicingState(Constants_order.INVOICE_STATE_HAND);
    save(order);
    // 存储发票与订单关联表的记录
    OrderInvoiceToOrder toOrder = new OrderInvoiceToOrder();
    toOrder.setOrderInvoiceId(orderInvoice.getId());
    toOrder.setOrderId(order.getId());
    toOrder.setState(Constants.YES);
    toOrder.setCreateTime(System.currentTimeMillis());
    orderInvoiceToOrderDao.save(toOrder);
    // 这里处理上传的附件
    handleUploadAttachment(orderInvoiceParams.getFileIdList(), order.getId());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void submitOrderAccept(SubmitOrderAcceptDTO submitOrderAcceptDTO) {
    List<DataInfo> dataInfo = submitOrderAcceptDTO.getDataInfo();
    String orderId = submitOrderAcceptDTO.getOrderId();
    Order order = get(orderId, () -> CheckException.noFindException(Order.class, orderId));

    int submitAcceptCount = 0;
    //上传的id
    List<String> uploadAcceptIdList = dataInfo.stream().map(DataInfo::getId).collect(Collectors.toList());
    handlerOrderAccept(orderId,uploadAcceptIdList);
    for (DataInfo info : CollUtil.emptyIfNull(dataInfo)) {
      String id = info.getId();
      OrderAccept orderAccept;
      List<String> fileIdList = CollUtil.emptyIfNull(info.getFileIdList());
      if (StrUtil.isBlank(id)) {
        orderAccept = new OrderAccept();
        orderAccept.setOrderId(order.getId());
        orderAccept.setCreateTime(System.currentTimeMillis());
        orderAccept.setFistUploadMan(submitOrderAcceptDTO.getUploadMan());
      } else {
        orderAccept =
            orderAcceptService.get(id, () -> CheckException.noFindException(OrderAccept.class, id));
        List<String> fileListId =
            StrUtil.split(orderAccept.getFileIds(), CharUtil.COMMA, true, true);
        for (String fileId : fileListId) {
          if (fileIdList.contains(fileId)) {
            continue;
          }
          if (fileService.existById(fileId)) {
            fileDao.delCurFileById(fileId);
          }
        }
        // 文件不传就是删除该签收信息
        if (CollUtil.isEmpty(fileIdList)) {
          orderAcceptService.delete(id);
          return;
        }
      }
      orderAccept.setFileIds(CollUtil.join(fileIdList, StrUtil.COMMA));
      orderAccept.setType(info.getType());
      orderAccept.setAuditStatus(Constants_order.ORDER_ACCEPT_PENDING_AUDITING);
      orderAcceptService.save(orderAccept);
      submitAcceptCount ++;
    }
    // 仅当提交的凭证数量大于 0 时，才修改订单上的凭证审核状态和上传时间
    if(submitAcceptCount > 0){
      order.setConfirmVoucherAuditStatus(Constants_order.ORDER_ACCEPT_PENDING_AUDITING);
      // 修改上传时间
      order.setUploadTime(System.currentTimeMillis());
      orderDao.save(order);
    }
  }

  private void handlerOrderAccept(String orderId, List<String> uploadAcceptIdList) {
    // 删除页面中删除的附件
    List<OrderAccept> orderAccepts = orderAcceptService.getByOrderId(orderId);
    List<String> allOrderAcceptIdList =
        orderAccepts.stream().map(OrderAccept::getId).collect(Collectors.toList());
    List<String> disjunction = CollUtil.subtractToList(allOrderAcceptIdList, uploadAcceptIdList);
    CollUtil.emptyIfNull(disjunction)
        .forEach(
            id -> {
              orderAcceptService.delete(id);
              log.info("删除：：{}", id);
            });
  }

  @Override
  public OrderInvoiceInfoDTO getOrderInvoiceApplyInfo(String orderInvoiceId) {
    Assert.notBlank(orderInvoiceId);
    OrderInvoice invoice = orderInvoiceDao.get(orderInvoiceId);
    if (ObjectUtils.isEmpty(invoice)) {
      throw new CheckException("发票id错误！");
    }
    List<OrderInvoiceToOrder> toOrders = orderInvoiceToOrderDao.getByInvoiceId(invoice.getId());
    if (ObjectUtils.isEmpty(toOrders)) {
      throw new CheckException("该发票信息暂缺，请联系管理员。");
    }
    OrderInvoiceToOrder toOrder = toOrders.get(0);
    String orderId = toOrder.getOrderId();
    Order order = get(orderId, () -> CheckException.noFindException(Order.class, orderId));
    OrderInvoiceInfoDTO orderInvoiceInfoDTO = new OrderInvoiceInfoDTO(invoice);
    orderInvoiceInfoDTO.setInvoiceType(order.getInvoicingState());
    List<FileDTO> fileList = new ArrayList<>();
    List<File> files =
        fileDao.getFileListByRelationIdAndTypesAndState(
            orderInvoiceId, ListUtil.of(Constants.FILE_TYPE_ODER_OTHER_FILE), Constants.STATE_OK);
    if (CollUtil.isEmpty(files)) {
      files =
          fileDao.getFileListByRelationIdAndTypesAndState(
              orderId, ListUtil.of(Constants.FILE_TYPE_ODER_OTHER_FILE), Constants.STATE_OK);
    }
    if (CollUtil.isNotEmpty(files)) {
      for (File file : files) {
        FileDTO fileDTO = new FileDTO(file);
        fileDTO.setBaseUrl(url);
        fileList.add(fileDTO);
      }
    }
    List<OrderOpenInvoice> orderOpenInvoices =
        CollUtil.emptyIfNull(orderOpenInvoiceService.getByInvoiceId(invoice.getId()));
    List<OpenInvoiceDTO> openInvoiceList = new ArrayList<>();
    for (OrderOpenInvoice orderOpenInvoice : orderOpenInvoices) {
      OpenInvoiceDTO openInvoiceDTO = new OpenInvoiceDTO(orderOpenInvoice);
      openInvoiceDTO.setFileDTO(
          CollUtil.emptyIfNull(
              StrUtil.split(orderOpenInvoice.getFileId(), CharUtil.COMMA, true, true).stream()
                  .map(
                      fileId ->
                          fileService.get(
                              fileId, () -> CheckException.noFindException(File.class, fileId)))
                  .map(file -> new FileDTO(file, url))
                  .collect(Collectors.toList())));
      openInvoiceList.add(openInvoiceDTO);
    }
    orderInvoiceInfoDTO.setOpenInvoiceList(openInvoiceList);
    orderInvoiceInfoDTO.setFileList(fileList);
    return orderInvoiceInfoDTO;
  }

  @Override
  public PageResult<AllowPaymentOrderDTO> getAllowPaymentOrderPage(
      AllowPaymentOrderQueryParam query, PageParam param) {
    String userId = query.getUserId();
    SupplierUser supplierUser =
        supplierUserService.get(
            userId, () -> CheckException.noFindException(SupplierUser.class, userId));
    String supplierId = supplierUser.getSupplierId();
    String schemeId = query.getSchemeId();
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(
              userId, Constants.SEARCH_TYPE_ORDER_ALLOW_PAYMENT);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotBlank(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotBlank(search.getContent())) {
        AllowPaymentOrderQueryParam allowPaymentOrderQuery =
            JSON.parseObject(
                search.getContent(), new TypeReference<AllowPaymentOrderQueryParam>() {});
        if (allowPaymentOrderQuery != null) {
          query.setOrderNo(
              StrUtil.blankToDefault(query.getOrderNo(), allowPaymentOrderQuery.getOrderNo()));
          query.setCustomer(
              StrUtil.blankToDefault(query.getCustomer(), allowPaymentOrderQuery.getCustomer()));
          query.setPlatform(
              StrUtil.blankToDefault(query.getPlatform(), allowPaymentOrderQuery.getPlatform()));
          query.setOrderTimeStart(
              ObjectUtil.defaultIfNull(
                  query.getOrderTimeStart(), allowPaymentOrderQuery.getOrderTimeStart()));
          query.setOrderTimeEnd(
              ObjectUtil.defaultIfNull(
                  query.getOrderTimeEnd(), allowPaymentOrderQuery.getOrderTimeEnd()));
          query.setOrderNoOrCustomer(
              ObjectUtil.defaultIfNull(
                  query.getOrderNoOrCustomer(), allowPaymentOrderQuery.getOrderNoOrCustomer()));
        }
      }
    }
    return PageResultBuilder.buildPageResult(
        orderDao.getMobileOrderPage(
            query.getOrderNo(),
            query.getCustomer(),
            query.getPlatform(),
            null,
            null,
            supplierId,
            Constants_order.WAIT_APPLY_PAYMENT_TYPE,
            query.getOrderTimeStart(),
            query.getOrderTimeEnd(),
            query.getOrderNoOrCustomer(),
            false,
            param.toPageable()),
        order -> {
          String orderTypeName = PlatformUtil.getPlatformNameByCode(order.getType());
          AllowPaymentOrderDTO allowPaymentOrderDTO =
              new AllowPaymentOrderDTO(order, orderTypeName);
          String accountId = orderAccountToOrderService.getAccountIdByOrderId(order.getId());
          allowPaymentOrderDTO.setAccountId(accountId);
          allowPaymentOrderDTO.setSignVoucherState(
              orderAcceptService.getAcceptState(order.getId()));
          allowPaymentOrderDTO.setPaymentMethodList(
              orderReceiptRecordService.findPaymentMethodsByOrderId(order.getId()));
          allowPaymentOrderDTO.setDefaultPurchase(
              StrUtil.blankToDefault(platformService.findDefaultPurchaseByCode(order.getType()),
                  srmConfig.getDefaultPurchase()));
          return allowPaymentOrderDTO;
        });
  }

  @Override
  public void setAccountStatusAllow(Order order) {
    Assert.notNull(order);
    // 这里是订单付款状态由不可付款->待申请的节点（满足回款三要素/1、客户回款状态完成；2、签收凭证已确认；3、供应商已开票）
    if (order.getConfirmVoucherTime() != null
        && Constants_order.RETURN_PROGRESS_ALL.equals(order.getCustomerReturnProgress())
        && Constants.ORDER_INVOICE_STATE_PASS.equals(order.getSupplierOpenInvoiceStatus())
        && Constants_order.CAN_NOT_PAYMENT_TYPE.equals(order.getPaymentStatus())) {
      order.setPaymentStatus(Constants_order.WAIT_APPLY_PAYMENT_TYPE);
      save(order);
      log.info("订单更改付款状态{}", order.getOrderNo());
    }
  }

  @Override
  @Transactional
  public void batchSubmitOrderInvoice(
      BatchOrderInvoiceParams batchOrderInvoiceParams, String userID) {
    Assert.notNull(batchOrderInvoiceParams);
    List<String> orderIds = batchOrderInvoiceParams.getOrderIds();
    List<Order> orders = new ArrayList<>();
    // 校验是否有订单已经提交开票申请
    orderIds.forEach(
        orderId -> {
          Order order;
          try {
            order = get(orderId, () -> CheckException.noFindException(Order.class, orderId));
          } catch (Exception e) {
            throw new RuntimeException("包含不存在的订单");
          }
          List<OrderInvoiceToOrder> orderToInvoices = orderInvoiceToOrderDao.getByOrderId(orderId);
          if (CollUtil.isNotEmpty(orderToInvoices)) {
            throw new CheckException("发票申请已经提交，请联系管理员！");
          }
          orders.add(order);
        });
    // 开票信息
    OrderInvoice orderInvoice = new OrderInvoice();
    // 被弃用的字段 orderInvoice.setOrderId(orderIds.get(0));
    orderInvoice.setType(batchOrderInvoiceParams.getOrderInvoiceEnums().getType());
    orderInvoice.setTitle(batchOrderInvoiceParams.getTitle());
    orderInvoice.setTaxNumber(batchOrderInvoiceParams.getTaxNumber());
    orderInvoice.setBankName(batchOrderInvoiceParams.getBankName());
    orderInvoice.setBankAccount(batchOrderInvoiceParams.getBankAccount());
    orderInvoice.setMobile(batchOrderInvoiceParams.getMobile());
    orderInvoice.setAddress(batchOrderInvoiceParams.getAddress());
    orderInvoice.setContent(batchOrderInvoiceParams.getContent());
    orderInvoice.setReceiveMan(batchOrderInvoiceParams.getReceiveMan());
    orderInvoice.setReceiveAddress(batchOrderInvoiceParams.getReceiveAddress());
    orderInvoice.setRemark(batchOrderInvoiceParams.getRemark());
    orderInvoice.setReceiveMobile(batchOrderInvoiceParams.getReceiveMobile());
    orderInvoice.setCreateTime(System.currentTimeMillis());
    orderInvoice.setInvoiceApplicationNumber(
        OrderUtil.generateInvoiceApplicationNumber(Constants_order.ORDER_INVOICE_TYPE_MERGE, null));
    OrderInvoice invoice = orderInvoiceService.save(orderInvoice);
    // 开票信息所关联的订单
    orders.forEach(
        order -> {
          order.setInvoicingState(Constants_order.INVOICE_STATE_HAND);
          save(order);
          OrderInvoiceToOrder toOrder = new OrderInvoiceToOrder();
          toOrder.setOrderInvoiceId(invoice.getId());
          toOrder.setOrderId(order.getId());
          toOrder.setState(Constants.YES);
          toOrder.setCreateTime(System.currentTimeMillis());
          toOrder.setCreateUserId(userID);
          orderInvoiceToOrderDao.save(toOrder);
        });
    if (CollectionUtils.isEmpty(batchOrderInvoiceParams.getOrderInvoiceFileDTOS())) {
      return;
    }
    String RELATION_TYPE = "orderOtherFile";
    batchOrderInvoiceParams
        .getOrderInvoiceFileDTOS()
        .forEach(
            orderInvoiceFileDTO -> {
              saveOrderInvoiceFile(orderInvoiceFileDTO, invoice.getId(), RELATION_TYPE);
            });
  }

  private void saveOrderInvoiceFile(OrderInvoiceFileDTO fileDTO, String relationId, String type) {
    String uploadMan = fileDTO.getUploadMan();
    String filename;
    if (ObjectUtil.isEmpty(fileDTO.getUrl()) || ObjectUtils.isEmpty(relationId)) {
      return;
    }
    File file = new File();
    String[] split = fileDTO.getUrl().split("/");
    filename = split[split.length - 1];
    if ("account".equals(type)
        || "invoice".equals(type)
        || "twoOrder".equals(type)
        || "orderInvoice".equals(type)
        || "orderOtherFile".equals(type)) {
      if ("account".equals(type)) {
        if (StrUtil.isBlank(uploadMan)) {
          throw new CheckException("上传人名称必传，请联系管理员！");
        }
        file.setRelationType(Constants.FILE_TYPE_YANSHOU);
      } else if ("invoice".equals(type)) {
        file.setRelationType(Constants.FILE_TYPE_INVOICE);
      } else if ("twoOrder".equals(type)) {
        file.setRelationType(Constants.FILE_TYPE_TWO_ORDERS);
        if (StrUtil.isBlank(uploadMan)) {
          throw new CheckException("上传人名称必传，请联系管理员！");
        }
      } else if ("orderInvoice".equals(type)) {
        file.setRelationType(Constants.FILE_TYPE_ODER_INVOICE);
      } else if ("orderOtherFile".equals(type)) {
        file.setRelationType(Constants.FILE_TYPE_ODER_OTHER_FILE);
      }

      file.setUploadMan(uploadMan);
      file.setRelationId(relationId);
      file.setName(filename);
      file.setUrl(fileDTO.getUrl());
      file.setDescription(filename);
      // 附件类型
      String fileLast = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
      String fileType = Constants.FILE_TYPE_PICTURE;
      if ("doc".equalsIgnoreCase(fileLast)
          || "docx".equalsIgnoreCase(fileLast)
          || "pdf".equalsIgnoreCase(fileLast)
          || "ppt".equalsIgnoreCase(fileLast)
          || "excel".equalsIgnoreCase(fileLast)) {
        fileType = Constants.FILE_TYPE_WORD;
      }
      if ("jpg".equalsIgnoreCase(fileLast)
          || "png".equalsIgnoreCase(fileLast)
          || "bmp".equalsIgnoreCase(fileLast)
          || "gif".equalsIgnoreCase(fileLast)) {
        fileType = Constants.FILE_TYPE_PICTURE;
      }
      if ("rar".equalsIgnoreCase(fileLast) || "zip".equalsIgnoreCase(fileLast)) {
        fileType = Constants.FILE_TYPE_RARZIP;
      }
      file.setType(fileType);
      file.setState(Constants.STATE_OK);
      file.setCreateTime(System.currentTimeMillis());
      fileRepository.save(file);
    }
  }

  @Override
  public List<OrderDetailVO> getOrderDetailsByApplicationNumber(String applicationNumber) {
    if (StringUtils.isNullOrEmpty(applicationNumber)) {
      throw new CheckException("参数错误");
    }
    OrderInvoice invoice =
        orderInvoiceDao.findOrderInvoiceByInvoiceApplicationNumber(applicationNumber);
    if (ObjectUtils.isEmpty(invoice)) {
      throw new CheckException("参数错误");
    }
    List<OrderInvoiceToOrder> invoiceToOrders =
        orderInvoiceToOrderDao.getByInvoiceId(invoice.getId());
    List<String> orderIds = new ArrayList<>();
    invoiceToOrders.forEach(
        invoiceToOrder -> {
          orderIds.add(invoiceToOrder.getOrderId());
        });
    return orderIds.stream()
        .map(
            orderId -> {
              return orderDetailService.getOrderDetail(orderId);
            })
        .collect(Collectors.toList());
  }

  @Override
  public OrderDetailInvoiceDTO getOrderDetailInvoiceByOrderId(String orderId) {
    Order order = get(orderId, () -> CheckException.noFindException(Order.class, orderId));
    return orderInvoiceTemplateService
        .getByOrderId(order.getId())
        .map(OrderDetailInvoiceDTO::new)
        .orElse(new OrderDetailInvoiceDTO());
  }

  private void handleUploadAttachment(List<String> fileIds, String relationId) {
    // 这里处理上传的附件
    List<String> fileIdList = CollUtil.emptyIfNull(fileIds);
    if (fileIdList.size() > 10) {
      throw new CheckException("最多上传 10 个附件");
    }
    List<File> files =
        CollUtil.emptyIfNull(
            fileDao.getByRelationIdAndTypes(
                relationId, ListUtil.of(Constants.FILE_TYPE_ODER_OTHER_FILE)));
    for (File file : files) {
      String id = file.getId();
      if (fileIdList.contains(id)) {
        continue;
      }
      File file1 = fileDao.get(id);
      if (file1 != null) {
        fileDao.delCurFileById(id);
      }
    }
  }

  @Override
  public List<ExpressCompanyDTO> getLogisticsCompanies() {
    List<ExpressCompanyDTO> logisticsCompanies = new ArrayList<>();
    return omsService.getLogisticsCompanies(null);
  }

  @Override
  public void updateAccountOpenInvoiceStatus(String orderId, String accountOpenInvoiceStatus) {
    if (StrUtil.isBlank(orderId) || StrUtil.isBlank(accountOpenInvoiceStatus)) {
      return;
    }
    Order order = orderDao.get(orderId);
    if (order == null) {
      return;
    }
    order.setSupplierOpenInvoiceStatus(accountOpenInvoiceStatus);
    orderDao.save(order);
  }

  @Override
  public long getOrderTotal(String supplierId, String accountStatus, String paymentStatus) {
    return orderDao.getOrderTotal(supplierId, accountStatus, paymentStatus, Boolean.FALSE);
  }

  @Override
  public List<String> getOrderLargeTicketProjectNo(
      Order order, String orderNo, String dockingOrderType) {
    return shareOrderService.getOrderLargeTicketProjectNo(order, orderNo, dockingOrderType);
  }


  @Override
  public List<OrderSomeStatusDTO> validateByOrderIds(String orderIdList) {
    String[] orderIds = orderIdList.split(",");
    List<OrderSomeStatusDTO> list = new ArrayList<>();
    for (String orderId : orderIds) {
      Order order = orderDao.get(orderId);
      if (order == null) {
        throw new CheckException("审核中供应商不能修改");
      }
      // 1 通过 2 异常
      OrderSomeStatusDTO dto = new OrderSomeStatusDTO();
      dto.setOrderId(orderId);
      if (order.getOrderTime() > time) {
        ERPOrderPayAmountDTO params = new ERPOrderPayAmountDTO();
        params.setOrderNo(order.getErpOrderNo());
        params.setDbId(erpConfig.getDbId());
        params.setUserName(erpConfig.getUserName());
        params.setPassWord(erpConfig.getPassword());
        // 调用erp应付单查询接口，看对应的采购订单号的应付金额是否》=派单金额，是 为通过
        BigDecimal orderPayAmount = erpRequest.getOrderPayAmount(params);
        if (orderPayAmount.compareTo(
                order.getPrice().subtract(order.getRefundPrice()).subtract(new BigDecimal("0.1")))
            > -1) {
          dto.setOrderSupplierOpenInvoiceStatusReview(PASS);
        } else {
          dto.setOrderSupplierOpenInvoiceStatusReview(UNPASS);
        }
      } else {
        dto.setOrderSupplierOpenInvoiceStatusReview(
            StrUtil.equals(order.getSupplierOpenInvoiceStatus(), Constants.ORDER_INVOICE_STATE_PASS)
                ? PASS
                : UNPASS);
      }
      dto.setSignVoucherStateReview(
          StrUtil.equals(
                  orderAcceptService.getAcceptState(order.getId()),
                  Constants_order.ORDER_ACCEPT_CONSENT)
              ? PASS
              : UNPASS);
      if (Objects.equals(
          order.getCustomerReturnProgress(), Constants_order.CUSTOMER_PAYBACK_CONFIRM)) {
        dto.setCustomerReturnProgressReview(PASS);
      } else {
        // 关联大票项目
        List<String> orderLargeTicketProjectNo =
            getOrderLargeTicketProjectNo(order, order.getOrderNo(), order.getType());
        String orgNos =
            Constants.DEFAULT_BIG_TICKET_ORGANIZATION_CODE.stream()
                .map(s -> StrUtil.wrap(s, "'"))
                .collect(Collectors.joining(","));
        String formatDate = DateUtil.formatDate(new Date());
        List<ReceivableBillDTO> receivableBillDTOS = new ArrayList<>();
        List<ReceivableReturnDTO> receivableReturnDTOS = new ArrayList<>();
        for (String projectNo : CollUtil.emptyIfNull(orderLargeTicketProjectNo)) {
          ReceivableQueryDTO receivableQueryDTO =
              buildQueryParam("2022-05-21", formatDate, projectNo, orgNos);
          // 回款单接口
          receivableBillDTOS.addAll(erpRequest.receivableBillQuery(receivableQueryDTO));
          // 应收单接口
          receivableReturnDTOS.addAll(erpRequest.receivableQuery(receivableQueryDTO));
        }
        // 调用erp回款单接口查询订单的大票回款金额>= 大票应收金额，大于等于为通过
        Set<ReceivableReturnDTO> receivableReturnDTOSet = null;
        BigDecimal totalPrice = new BigDecimal(BigInteger.ZERO);
        if (CollUtil.isNotEmpty(receivableReturnDTOS)) {
          removeInternalCustomers(receivableReturnDTOS);
          receivableReturnDTOSet = new HashSet<>(receivableReturnDTOS);
          totalPrice =
              receivableReturnDTOSet.stream()
                  .map(receivableReturnDTO -> new BigDecimal(receivableReturnDTO.getAmount()))
                  .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        BigDecimal returnPrice = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(receivableBillDTOS)) {
          removeInternalCustomers(receivableReturnDTOS);
          returnPrice =
              receivableBillDTOS.stream()
                  .map(receivableBillDTO -> new BigDecimal(receivableBillDTO.getAmount()))
                  .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        dto.setCustomerReturnProgressReview(
            (returnPrice.compareTo(totalPrice) == 0 || returnPrice.compareTo(totalPrice) == 1)
                ? PASS
                : UNPASS);
      }
      list.add(dto);
    }
    return list;
  }

  /** 排除内部客户 */
  private void removeInternalCustomers(
      final List<? extends BaseReceivableReturnDTO> baseReceivableReturnDTOs) {
    if (CollUtil.isEmpty(baseReceivableReturnDTOs)) {
      return;
    }
    Map<String, String> customerNames =
        Constants.EXCLUDE_INSIDE_CUSTOMER_NAME_LIST.stream()
            .collect(
                Collectors.toMap(
                    s -> {
                      return s;
                    },
                    s -> {
                      return s;
                    }));
    Map<String, String> customerCodes =
        Constants.EXCLUDE_INSIDE_CUSTOMER_CODE_LIST.stream()
            .collect(
                Collectors.toMap(
                    s -> {
                      return s;
                    },
                    s -> {
                      return s;
                    }));
    ArrayList<BaseReceivableReturnDTO> removeSet = new ArrayList<>();
    for (BaseReceivableReturnDTO baseReceivableReturnDTO : baseReceivableReturnDTOs) {
      String customerName = baseReceivableReturnDTO.getCustomerName();
      String customerNo = baseReceivableReturnDTO.getCustomerNo();
      String isContainName = customerNames.get(customerName);
      String isContainCode = customerCodes.get(customerNo);
      if (isContainName != null || isContainCode != null) {
        removeSet.add(baseReceivableReturnDTO);
      }
    }
    baseReceivableReturnDTOs.removeAll(removeSet);
  }

  @Override
  public List<CustomerPaybackDTO> getCustomerPaybackList(CustomerPaybackParams params) {
    List<CustomerPaybackDTO> list = new ArrayList<>();
    List<Order> customerPaybackList =
        orderDao.getCustomerPaybackList(
            params.getSupplierId(), params.getPlatform(), params.getCustomer());
    customerPaybackList.parallelStream()
        .forEach(
            order -> {
              BigDecimal allSalesAmount = BigDecimal.ZERO;
              BigDecimal allRefundPrice = BigDecimal.ZERO;
              List<Order> orderAny =
                  orderDao.getOrderByPlatformCodeAndCustomerAndSupplierId(
                      params.getSupplierId(), order.getType(), order.getCustomer());
              for (Order orderInfo : orderAny) {
                String dockingOrderType =
                    StrUtil.emptyToDefault(order.getSubType(), order.getType());
                PaymentStatus orderCustomerReturn =
                    httpUtil.getOrderCustomerReturn(
                        orderInfo.getOrderNo(), dockingOrderType, false);
                if (orderCustomerReturn != null) {
                  allSalesAmount =
                      allSalesAmount.add(
                          new BigDecimal(
                              orderCustomerReturn.getPlatformPrice() != null
                                  ? orderCustomerReturn.getPlatformPrice()
                                  : "0"));
                  allRefundPrice =
                      allRefundPrice.add(
                          new BigDecimal(
                              orderCustomerReturn.getSkAmount() != null
                                  ? orderCustomerReturn.getSkAmount()
                                  : "0"));
                }
              }

              CustomerPaybackDTO dto = new CustomerPaybackDTO();
              OrderPlatformDTO orderPlatform = platformService.findByCode(order.getType());
              if (orderPlatform != null && StrUtil.isNotBlank(orderPlatform.getPlatformName())) {
                dto.setPlatform(orderPlatform.getPlatformName());
              }
              dto.setCustomer(order.getCustomer());
              dto.setAllSalesAmount(
                  new BigDecimal(CommonlyUseUtil.BigDecimalValue(allSalesAmount.toPlainString())));
              dto.setAllRefundPrice(
                  new BigDecimal(CommonlyUseUtil.BigDecimalValue(allRefundPrice.toPlainString())));
              dto.setUnRefundPrice(
                  new BigDecimal(
                      CommonlyUseUtil.BigDecimalValue(
                          allSalesAmount.subtract(allRefundPrice).toPlainString())));
              list.add(dto);
            });

    return list;
  }

  private ReceivableQueryDTO buildQueryParam(
      String time, String endTime, String projectNum, String orgNos) {
    return ReceivableQueryDTO.builder()
        .dbId(erpConfig.getDbId())
        .userName(erpConfig.getUserName())
        .passWord(erpConfig.getPassword())
        .model(
            Model.builder()
                .projectNum(projectNum)
                .projectName(StrUtil.EMPTY)
                .startTime(time)
                .endTime(endTime)
                .orgNos(orgNos)
                .build())
        .build();
  }

  @Override
  public Long getInvoiceCountByType(String type, String supplierId) {
    Assert.notBlank(type);
    Assert.notBlank(supplierId);
    return orderDao.countOrderInvoiceBySupplierId(type, supplierId);
  }

  @Override
  public PageResult<MobileOrderTableDTO> get(String supplierId,String orderNo, Integer pageNo, Integer pageSize) {
    List<String> orderPageStates =
        ListUtil.toList(
//            Constants_order.ORDER_STATE_WAIT,
//            Constants_order.ORDER_STATE_PROCESS,
//            Constants_order.ORDER_STATE_ACCEPTING,
//            Constants_order.ORDER_STATE_ACCEPTED
        );
    List<String> supplierOrderPageStates =
        ListUtil.toList(
//            SupplierOrderState.WAIT.getOrderState(),
//            SupplierOrderState.IN_PROGRESS.getOrderState(),
//            SupplierOrderState.COMPLETE.getOrderState()
        );
    return PageResultBuilder.buildPageResult(
        orderDao.getByOrderNo(
            supplierId,
            orderNo,
            Constants_order.ORDER_STATE_WITHDRAW,
            orderPageStates,
            supplierOrderPageStates,
            pageNo,
            pageSize),
        objects -> {
          String type = (String) objects[0];
          OrderType orderType = OrderType.getByType(type);
          OrderTableDTO order = null;
          SupplierOrderTableDTO supplierOrder = null;
          switch (Objects.requireNonNull(orderType)) {
            case ORDER:
              order = buildOrderTableDTO(objects);
              break;
            case SUPPLIER_ORDER:
              supplierOrder = buildSupplierOrderTableDTO(objects);
              break;
            default:
              log.error("订单类型错误");
              return null;
          }
          return new MobileOrderTableDTO(orderType, order, supplierOrder);
        });
  }

  @Override
  public GetCountDTO getPendingNum(String supplierId) {
    // 待处理订单
    long orderWaitCount =
        orderDao.getOrderCountBySupplierIdAndType(
            supplierId,
            ListUtil.toList(Constants_order.ORDER_STATE_WAIT, Constants_order.ORDER_STATE_PROCESS));
    //排除全部发货的订单
    long supplierOrderWaitCount =
        supplierOrderService.getSupplierOrderDeliveryCount(
            supplierId,
            null,
            ListUtil.toList(
                SupplierOrderState.WAIT.getOrderState(),
                SupplierOrderState.IN_PROGRESS.getOrderState()),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);
    // 可录入发票总计
    long canInvoiceOrderTotal =
        orderDao.getCanInvoiceOrderTotal(supplierId, Constants.ORDER_INVOICE_STATE_NOT_DONE);
    long supplierOrderCount =
        supplierOrderService.getTotalByOrderState(
            supplierId, SupplierOrderState.COMPLETE.getOrderState());
    // 统计可付款订单的订单数量
    long payableOrderTotal =
        orderService.getOrderTotal(supplierId, null, Constants_order.WAIT_APPLY_PAYMENT_TYPE);
    //待验收数量
    return GetCountDTO.builder()
        .deliveryCount(supplierOrderWaitCount + orderWaitCount)
        .invoiceCount(canInvoiceOrderTotal+supplierOrderCount)
        .paymentApplicationCount(
            orderPaymentDao.getOrderPaymentNumByPaymentStatus(supplierId, null))
        .acceptanceCount(
            orderDao.getOrderCountBySupplierIdAndType(
                supplierId, Constants_order.ORDER_STATE_ACCEPTING))
        .orderCount(orderWaitCount)
        .supplierOrderCount(supplierOrderWaitCount)
        .payableOrdersTotal(payableOrderTotal)
        .build();
  }

  @Override
  public PageResult<OrderDeliveryPageDTO> getDeliveryOrderPage(
      DeliveryOrderQueryParam param, String supplierId) {
    String orderPageState = param.getOrderPageState();
    List<String> orderPageStates = new ArrayList<>();
    if (StrUtil.isNotBlank(orderPageState)) {
      orderPageStates.add(orderPageState);
    } else {
      orderPageStates.add(Constants_order.ORDER_STATE_WAIT);
      orderPageStates.add(Constants_order.ORDER_STATE_PROCESS);
    }
    Page<Order> page =
        orderDao.getDeliveryOrderPage(
            supplierId,
            param.getOrderNo(),
            param.getOrderState(),
            param.getConsignee(),
            param.getMobile(),
            param.getAddress(),
            orderPageStates,
            param.getInvoicingState(),
            param.getStartDate(),
            param.getEndDate(),
            param.getPlatform(),
            param.getPageNo(),
            param.getPageSize());
    return PageResultBuilder.buildPageResult(
        page,
        order -> {
          String name = platformService.findNameByCode(order.getType());
          OrderDeliveryPageDTO data = new OrderDeliveryPageDTO(order, name);
          Object[] count = orderDetailDao.getReturnCountAndShipCountAndSkuNumCount(order.getId());
          // 已发数量
          BigDecimal totalReturnNum = (BigDecimal) count[0];
          BigDecimal totalShipNum = (BigDecimal) count[1];
          BigDecimal totalNum = (BigDecimal) count[2];
          // 取消数量
          BigDecimal totalCancelNum = (BigDecimal) count[3];
          if (totalNum.compareTo(BigDecimal.ZERO) > 0) {
            data.setProgress(
                NumberUtil.sub(totalShipNum, totalReturnNum)
                    + "/"
                    + NumberUtil.sub(totalNum, totalReturnNum, totalCancelNum));
          } else {
            data.setProgress("-");
          }
          return data;
        });
  }

  @Override
  public PageResult<SupplierOrderDeliveryDTO> getSupplierOrderDeliveryPage(
      SupplierOrderDeliveryQueryParam param, String supplierId) {
    return supplierOrderService.getSupplierOrderDeliveryPage(param, supplierId);
  }

  @Override
  public OrderDeliveryCountDTO getOrderDeliveryCount(String supplierId) {
    // 待处理订单
    long orderWaitCount =
        orderDao.getOrderCountBySupplierIdAndType(
            supplierId,
            ListUtil.toList(Constants_order.ORDER_STATE_WAIT, Constants_order.ORDER_STATE_PROCESS));
    long supplierOrderWaitCount =
        supplierOrderService.getSupplierOrderDeliveryCount(
            supplierId,
            null,
            ListUtil.toList(
                SupplierOrderState.WAIT.getOrderState(),
                SupplierOrderState.IN_PROGRESS.getOrderState()),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);
    return OrderDeliveryCountDTO.builder()
        .waitCount(supplierOrderService.getWaitOrderCount(supplierId))
        .orderCount(orderWaitCount)
        .supplierOrderCount(supplierOrderWaitCount)
        .build();
  }

  @Override
  public LogisticsInfoDTO identifyingWaybill(String url) {
    return httpUtil.getLogisticsInfoByUrl(OrcParam.builder().imageUrl(url).build());
  }

  @Override
  public JSONArray getLogisticsInformation(LogisticsInformationParam param) {
    JSONArray logisticsStatus;
    try {
      logisticsStatus =
          dockService.getLogisticsStatus(
              param.getExpressNo(), param.getExpressCode(), param.getPhoneNumber());
      log.info("供应商订单发货单物流查询返回：{}", logisticsStatus);
    } catch (Exception e) {
      log.warn("供应商订单发货单物流查询失败,单号：{}", param.getExpressNo());
      return new JSONArray();
    }
    return logisticsStatus;
  }

  private OrderTableDTO buildOrderTableDTO(Object[] object) {
    String id = (String) object[1];
    String code = (String) object[2];
    String orderStatus = (String) object[3];
    // 下单平台
    String type = (String) object[4];
    BigInteger createTime = (BigInteger) object[5];
    BigInteger orderTime = (BigInteger) object[6];
    BigDecimal price = (BigDecimal) object[7];
    return new OrderTableDTO(
        id, code, orderStatus, orderTime.longValue(), createTime.longValue(), price, type);
  }

  private SupplierOrderTableDTO buildSupplierOrderTableDTO(Object[] object) {
    String id = (String) object[1];
    String code = (String) object[2];
    String orderStatus = (String) object[3];
    // 采购组织
    String groupName = (String) object[4];
    BigInteger createTime = (BigInteger) object[5];
    // 是否厂直发
    BigInteger directShipment = (BigInteger) object[6];
    // 采购件数
    BigDecimal number = (BigDecimal) object[7];
    String price = (String) object[8];
    return new SupplierOrderTableDTO(
        id,
        code,
        orderStatus,
        groupName,
        createTime!=null?createTime.longValue():null,
        BooleanUtil.toBoolean(directShipment.toString()),
        number,
        NumberUtil.toBigDecimal(price));
  }
}
